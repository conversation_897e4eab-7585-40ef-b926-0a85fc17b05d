.spinner > div {
  width: 6px;
  height: 6px;
  background-color: #fff;
  border-radius: 100%;
  display: inline-block;
  -webkit-animation: sk-bouncedelay 1.4s infinite ease-in-out both;
  animation: sk-bouncedelay 1.4s infinite ease-in-out both;
}

.spinner .bounce1 {
  -webkit-animation-delay: -0.32s;
  animation-delay: -0.32s;
}

.spinner .bounce2 {
  -webkit-animation-delay: -0.16s;
  animation-delay: -0.16s;
}

@-webkit-keyframes sk-bouncedelay {
  0%,
  80%,
  100% {
    -webkit-transform: scale(0);
  }
  40% {
    -webkit-transform: scale(1);
  }
}

@keyframes sk-bouncedelay {
  0%,
  80%,
  100% {
    -webkit-transform: scale(0);
    transform: scale(0);
  }
  40% {
    -webkit-transform: scale(1);
    transform: scale(1);
  }
}
html,
body,
div,
p,
ul,
li,
h1,
h2,
h3,
h4,
h5,
h6 {
  margin: 0;
  padding: 0;
}
body {
  font-family: -apple-system;
  font-size: 12px;
  color: #000;
  background: #fff;
}

body,
html {
  -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
}

button,
input {
  border: none;
  background: none;
  outline: 0;
}

a {
  text-decoration: none;
}

ul,
li {
  list-style: none;
}

strong,
b,
em {
  font-weight: normal;
  font-style: normal;
}
textarea,
input {
  appearance: none;
  -webkit-appearance: none;
}
.btn {
  display: block;
  width: 50%;
  padding: 4px 15px;
  background: rgba(4, 119, 249, 1);
  border: rgba(4, 119, 249, 1) 1px solid;
  border-radius: 15px;
  text-align: center;
  color: #fff;
  font-size: 14px;
}

.btn i {
  width: 0.3rem;
  height: 0.3rem;
  text-indent: -99999px;
  position: relative;
  display: inline-block;
  vertical-align: middle;
  margin: -0.06rem 0.1rem 0 0.1rem;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 15px;
  border: 1px solid rgba(225, 225, 225, 0.2);
  border-right-color: #fff;
  overflow: hidden;
  -webkit-animation: three-quarters-loader 700ms infinite
    cubic-bezier(0, 0, 0.75, 0.91);
          animation: three-quarters-loader 700ms infinite
    cubic-bezier(0, 0, 0.75, 0.91);
}

.btn.grey {
  border-color: #cacaca;
  background: #cacaca;
}

.step3 em {
  display: none;
}

.download-loading {
  position: relative;
  background: #dbdde2;
  overflow: hidden;
  width: 100px !important;
}

.download-loading i {
  display: none;
}

.download-loading span {
  position: relative;
  z-index: 1;
}
.download-loading span b {
  display: inline-block;
  vertical-align: middle;
  margin-top: -2px;
  width: 50px;
}
.download-loading em {
  display: block;
  position: absolute;
  display: block;
  top: 0;
  left: 0;
  width: 0;
  height: 100%;
  background: rgba(4, 119, 249, 1);
}

@-webkit-keyframes three-quarters-loader {
  from {
    -webkit-transform: rotate(0);
            transform: rotate(0);
  }

  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

@keyframes three-quarters-loader {
  from {
    -webkit-transform: rotate(0);
            transform: rotate(0);
  }

  to {
    -webkit-transform: rotate(360deg);
            transform: rotate(360deg);
  }
}

.btn-mini {
  display: inline-block;
  width: auto;
}

.clr:after {
  display: table;
  clear: both;
  overflow: hidden;
}

.clr {
  zoom: 1;
}
.blue-color {
  color: #0070c9 !important;
}
/* banner */
.contain-page {
  max-width: 750px;
  margin: 0 auto;
}

.app-banner {
  display: none;
}

.app-banner img {
  display: block;
  width: 100%;
  height: 4rem;
}

/* info */
.app-info {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  padding: 20px 0;
  width: 87.5%;
  margin: 0 auto;
  background: #fff;
}

.app-logo {
  float: left;
  width: 28%;
  margin-right: 10px;
}

.app-logo img {
  display: block;
  width: 100%;
  border-radius: 20px;
}

.app-info-rig {
  -webkit-box-flex: 1;
      -ms-flex: 1;
          flex: 1;
}

.app-info-rig strong {
  display: block;
  margin-top: 6px;
  margin-left: 3.28358%;
  font-size: 20px;
  font-weight: bold;
}

.app-info-rig p {
  margin: 0.3em 0 0 3.28358%;
  font-size: 14px;
  color: #8a8a90;
}
.app-info-rig .clr {
  margin-top: 1.8em;
}
.arouse {
  float: right;
  height: 30px;
  line-height: 30px;
  border-radius: 15px;
  text-align: center;
  font-size: 12px;
  color: rgba(6, 122, 254, 1);
}
.arouse b {
  display: inline-block;
  vertical-align: middle;
  width: 20px;
  height: 20px;
  line-height: 20px;
  margin: -2px 5px 0 0;
  text-align: center;
  background: rgba(6, 122, 254, 1);
  color: #fff;
  border-radius: 100%;
}

.app-show {
  padding: 0 0 20px;
  width: 87.5%;
  margin: 0 auto;
  background: #fff;
  color: #8e8f92;
}

.app-age {
  float: right;
}
.app-score strong,
.app-age strong {
  font-size: 16px;
  font-weight: bold;
}

.app-score p,
.app-age p {
  color: #d8d8d8;
  font-size: 12px;
}

.app-score img {
  width: 80px;
  margin-left: 5px;
}

.app-age {
  text-align: right;
}

/* intro */
.app-intro,
.comment-box,
.information-box {
  margin: 0 auto;
  padding: 20px 0;
  width: 87.5%;
  border-top: 1px solid #e5e5e5;
}

.app-title {
  margin-bottom: 0.85em;
  font-size: 20px;
}

.app-intro-con {
  position: relative;
  line-height: 1.8;
  font-size: 14px;
  height: 5.4em;
  overflow: hidden;
}

.app-intro-con.open {
  height: auto;
}

.app-intro-con span {
  display: none;
  position: absolute;
  right: 0;
  bottom: 0;
  padding-left: 1em;
  background: #fff;
  color: #067afe;
}

/* 过程 */
.app-flow {
  display: none;
  margin: 0.5rem 0.4rem 0;
}

.appSteps {
  position: relative;
  counter-reset: step;
  margin: 0.4rem 0 0.5rem;
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
}

.appSteps li {
  list-style-type: none;
  font-size: 0.9rem;
  text-align: center;
  width: 50%;
  position: relative;
  float: left;
  color: rgb(164, 164, 164);
}

.appSteps li .step {
  display: block;
  width: 0.54rem;
  height: 0.54rem;
  background-color: rgb(233, 239, 245);
  line-height: 0.54rem;
  border-radius: 100%;
  font-size: 0.32rem;
  color: #fff;
  text-align: center;
  font-weight: 700;
  margin: 0 auto 0.16rem;
}

.appSteps li p {
  font-size: 0.24rem;
}

.appSteps li::after {
  content: "";
  width: 60%;
  height: 1px;
  background-color: rgb(233, 239, 245);
  position: absolute;
  left: -30%;
  top: 0.27rem;
  z-index: -1;
}

.appSteps li:first-child::after {
  display: none;
}

.appSteps.step01 li:nth-of-type(1) .step {
  background-color: rgb(51, 135, 255);
}

.appSteps.step02 li:nth-of-type(1) .step {
  background-color: rgb(233, 239, 245);
  text-indent: -9999px;
  position: relative;
}

.appSteps.step02 li:nth-of-type(2) .step {
  background-color: rgb(51, 135, 255);
}

.appSteps.step02 li:nth-of-type(2)::after {
  background-color: rgb(51, 135, 255);
}

.appSteps.step02 li:nth-of-type(1) .step::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0.26rem;
  height: 0.21rem;
  background-image: url("../imgs/gou.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.appSteps.step03 li:nth-of-type(1) .step {
  background-color: rgb(233, 239, 245);
  text-indent: -9999px;
  position: relative;
}

.appSteps.step03 li:nth-of-type(2) .step {
  background-color: rgb(233, 239, 245);
  text-indent: -9999px;
  position: relative;
}

.appSteps.step03 li:nth-of-type(3) .step {
  background-color: rgb(51, 135, 255);
}

.appSteps.step03 li:nth-of-type(2)::after {
  background-color: rgb(51, 135, 255);
}

.appSteps.step03 li:nth-of-type(3)::after {
  background-color: rgb(51, 135, 255);
}

.appSteps.step03 li:nth-of-type(1) .step::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0.26rem;
  height: 0.21rem;
  background-image: url("../imgs/gou.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.appSteps.step03 li:nth-of-type(2) .step::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0.26rem;
  height: 0.21rem;
  background-image: url("../imgs/gou.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

.appSteps.step04 li:nth-of-type(1)::after,
.appSteps.step04 li:nth-of-type(2)::after,
.appSteps.step04 li:nth-of-type(3)::after {
  background-color: rgb(51, 135, 255);
}

.appSteps.step04 li:nth-of-type(1) .step,
.appSteps.step04 li:nth-of-type(2) .step,
.appSteps.step04 li:nth-of-type(3) .step {
  background-color: rgb(233, 239, 245);
  text-indent: -9999px;
  position: relative;
}

.appSteps.step04 li:nth-of-type(1) .step::after,
.appSteps.step04 li:nth-of-type(2) .step::after,
.appSteps.step04 li:nth-of-type(3) .step::after {
  content: "";
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0.26rem;
  height: 0.21rem;
  background-image: url("../imgs/gou.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
}

/* 引导 */
.app-guide {
  display: none;
  margin: 0.85rem 0.3rem 1rem 0.1rem;
}

.app-guide img {
  display: block;
  width: 100%;
}

.app-guide li {
  margin-top: 0.3rem;
}

.app-guide p {
  margin: 0.15rem 0.1rem 0 0.3rem;
}

.app-guide p em {
  font-weight: bold;
}

.comment-left strong {
  font-size: 60px;
  line-height: 43px;
  color: #4a4a4e;
  font-weight: bold;
}

.comment-left p {
  width: 91px;
  text-align: center;
  color: #7b7b7b;
  margin-top: 10px;
}

.comment-star-list li {
  line-height: 1;
}
.comment-right {
  float: right;
  width: 63.38308%;
}
.comment-right p {
  margin-top: 5px;
  color: #7b7b7b;
  text-align: right;
}

.comment-star,
.comment-progress {
  display: inline-block;
  vertical-align: middle;
}

.comment-star {
  position: relative;
  width: 46px;
  height: 7px;
}

.comment-star img {
  display: block;
  width: 100%;
  height: 100%;
}

.comment-star div {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  background: #fff;
}

.comment-progress {
  position: relative;
  width: calc(100% - 56px);
  height: 2px;
  background: #e9e9ec;
  border-radius: 2px;
}

.comment-progress div {
  position: absolute;
  width: 0;
  height: 2px;
  background: #4a4a4e;
  border-radius: 2px;
}

.comment-star-list li:nth-child(1) .comment-progress div {
  width: 90%;
}

.comment-star-list li:nth-child(2) .comment-progress div {
  width: 10%;
}

.comment-star-list li:nth-child(2) .comment-star div {
  width: 20%;
}

.comment-star-list li:nth-child(3) .comment-star div {
  width: 40%;
}

.comment-star-list li:nth-child(4) .comment-star div {
  width: 60%;
}

.comment-star-list li:nth-child(5) .comment-star div {
  width: 80%;
}

/* 信息 */

.information-list li {
  /* display: flex; */
  line-height: 3.5;
  border-bottom: #f2f2f2 1px solid;
  overflow: hidden;
}

.information-list li .l {
  float: left;
  color: #737379;
}

.information-list li .r {
  /* flex: 1; */
  overflow: hidden;
  text-align: right;
}
.information-list li .r p {
  display: inline-block;
  vertical-align: middle;
  width: 80%;
  line-height: 1.2;
}
.information-list li:last-child {
  border: none;
}

/* 展开 */
.open-btn {
  float: right;
  font-size: 0.26rem;
  line-height: 0.48rem;
  color: #067afe;
}

.hidden {
  display: none;
}

.mask {
  z-index: 998;
  display: none;
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.5);
}

.mask img {
  position: absolute;
  top: 0;
  right: 0;
  width: 80%;
}

.disclaimer {
  padding: 10px;
  color: rgba(153, 153, 153, 1);
  background: rgba(249, 249, 249, 1);
}
/* 弹框流程 */
.mask-box {
  z-index: 999;
  position: relative;
  display: none;
}

.mask-colsed {
  z-index: 2;
  position: absolute;
  right: 15px;
  top: 15px;
  width: 15px;
}

.mask-colsed img {
  display: block;
  width: 100%;
}

.mask-bg {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.2);
}

.mask-pop {
  position: fixed;
  top: 50%;
  left: 50%;
  width: 80%;
  max-width: 300px;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  background: #fff;
  border-radius: 15px;
  overflow: hidden;
}
.video-pop {
  width: 250px;
  background: none;
  overflow: visible;
}
.video-pop .prism-player {
  background: none;
}
.video-pop .mask-colsed {
  right: -25px;
  top: 0;
}
.video-pop #video {
  display: block;
  width: 100%;
}
/*  */
.copy-url-img {
  display: block;
  width: 100%;
}

.copy-url {
  position: relative;
  margin: 20px 30px;
  height: 36px;
  line-height: 36px;
  background: #f1f6f9;
  border-radius: 18px;
  overflow: hidden;
}

.copy-url input {
  padding-left: 20px;
  color: #535352;
}

.copy-url button {
  position: absolute;
  right: 0;
  top: 0;
  padding: 0 15px;
  height: 36px;
  line-height: 36px;
  background: -webkit-gradient(
    linear,
    left top, right top,
    from(rgba(34, 125, 249, 1)),
    to(rgba(0, 203, 250, 1))
  );
  background: -o-linear-gradient(
    left,
    rgba(34, 125, 249, 1),
    rgba(0, 203, 250, 1)
  );
  background: linear-gradient(
    90deg,
    rgba(34, 125, 249, 1),
    rgba(0, 203, 250, 1)
  );
  color: #fff;
  border-radius: 0 18px 18px 0;
}

/*  */
.file-info {
  display: block;
  margin: 30px 0 20px;
  font-size: 14px;
  color: #00b0f9;
  text-align: center;
}

.file-box {
  z-index: 2;
  display: none;
  position: fixed;
  top: 50%;
  left: 50%;
  padding: 20px;
  width: 70%;
  max-width: 300px;
  -webkit-transform: translate(-50%, -50%);
      -ms-transform: translate(-50%, -50%);
          transform: translate(-50%, -50%);
  background: #fff;
  border-radius: 20px;
}

.file-box h3 {
  text-align: center;
  font-size: 16px;
  color: #3a3a3a;
}

.file-con {
  margin: 20px 0;
  font-size: 14px;
  color: #777;
}

.file-con strong {
  display: block;
  margin-top: 20px;
  color: #333;
}

.file-con p {
  margin-top: 8px;
}

.colsed-btn {
  display: block;
  margin: 0 auto;
  width: 80%;
  height: 40px;
  line-height: 40px;
  background: -webkit-gradient(
    linear,
    left top, right top,
    from(rgba(32, 124, 249, 1)),
    to(rgba(0, 205, 250, 1))
  );
  background: -o-linear-gradient(
    left,
    rgba(32, 124, 249, 1),
    rgba(0, 205, 250, 1)
  );
  background: linear-gradient(
    90deg,
    rgba(32, 124, 249, 1),
    rgba(0, 205, 250, 1)
  );
  border-radius: 20px;
  font-size: 14px;
  color: #fff;
  text-align: center;
}

/* swiper */
.swiper-container {
  width: 100%;
}

.swiper-slide img {
  display: block;
  width: 100%;
}

.swiper-slide p {
  margin: 10px 0;
  text-align: center;
  font-size: 14px;
  color: #0491f7;
}

.mask-pop .swiper-container .swiper-pagination {
  position: static;
}

.swiper-pagination-bullet {
  background: #dbf0fd;
  opacity: 1;
}

.swiper-pagination-bullet-active {
  background: #0491f7;
}

/* 加载框 */
.loading-box {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(255, 255, 255, 0.9);
}

.loading-box span {
  display: block;
  position: absolute;
  top: 50%;
  left: 50%;
  margin: -0.8rem 0 0 -0.8rem;
  width: 1.6rem;
  height: 1.6rem;
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  border-radius: 100%;
  border: 0.08rem solid rgba(22, 39, 65, 0.2);
  border-right-color: #2a9ff6;
  overflow: hidden;
  -webkit-animation: three-quarters-loader 700ms infinite
    cubic-bezier(0, 0, 0.75, 0.91);
          animation: three-quarters-loader 700ms infinite
    cubic-bezier(0, 0, 0.75, 0.91);
}
/* 电脑展示 */
.pc-box {
  display: none;
  text-align: center;
}
.pc-box .info {
  font-size: 16px;
  font-weight: bold;
}
.pc-logo {
  width: 160px;
  border-radius: 20px;
  overflow: hidden;
  margin: 20px auto 0;
}
.pc-logo img {
  display: block;
  width: 100%;
}
.pc-box > p {
  font-size: 20px;
  font-weight: 400;
  line-height: 1.5em;
}
.pc-box .code {
  width: 231px;
  height: 231px;
}
/* 图片展示 */
.imgs-box {
  width: 87.5%;
  margin: 0 auto 20px;
}
.imgs-box .swiper-slide {
  display: inline-block;
  vertical-align: bottom;
  width: auto;
  margin: 0;
  margin-right: 3.3vw;
  padding: 0;
  padding-bottom: 0.75em;
  white-space: normal;
  font-size: 12px;
}
.imgs-box .swiper-slide img {
  display: block;
  width: auto;
  height: auto;
  min-width: 52vw;
  max-width: 82vw;
  max-height: 65vh;
  border-radius: 10px;
}
/* 输入下载码 */
.pay-box {
  display: none;
  position: relative;
  z-index: 1000;
}
.custom-title {
  line-height: 80px;
  text-align: center;
  font-size: 16px;
  color: #888;
}
.pay-bg {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background: rgba(0, 0, 0, 0.4);
}

.pay-pop {
  position: fixed;
  top: 20%;
  left: 10%;
  /* transform: translate(-50%, -50%); */
  width: 80%;
  background: #fff;
  border-radius: 10px;
}

.pay-pop > strong {
  display: block;
  padding: 15px 0 0;
  color: #000;
  font-size: 16px;
  font-weight: bold;
  text-align: center;
}

.next-btn {
  display: block;
  width: 100%;
  height: 42px;
  line-height: 42px;
  border-radius: 0 0 10px 10px;
  background: #0477f9;
  color: #fff;
  font-size: 14px;
  text-align: center;
}

.pay-pop .colsed {
  position: absolute;
  right: 20px;
  top: 20px;
  width: 15px;
  height: 15px;
}
.pay-pop .colsed img {
  display: block;
  width: 100%;
  height: 100%;
}
.invitation-code-input {
  width: 80%;
  margin: 20px auto;
}
.invitation-code-input > input {
  -webkit-box-sizing: border-box;
          box-sizing: border-box;
  display: block;
  width: 100%;
  padding: 10px 10px;
  border: 1px solid #e5e5e5;
}
.invitation-code-input .error {
  display: none;
  margin-top: 10px;
  color: #f00;
}

.pay-pop > p {
  display: block;
  padding: 5px 0 10px;
  color: #c4a916;
  font-size: 14px;
  text-align: center;
  border-bottom: #c1c1c1 1px solid;
}
.pay-pop .wait {
  display: block;
  padding-bottom: 30px;
  text-align: center;
  color: #c1c1c1;
  font-size: 14px;
}

.radio input[type="radio"] {
  opacity: 0;
}

.radio input[type="radio"] + span {
  position: relative;
  display: inline-block;
  width: 20px;
  height: 20px;
  border: #eee 1px solid;
  border-radius: 100%;
}

.radio input[type="radio"]:checked + span {
  border-color: #0477f9;
  background: #0477f9;
}

.radio input[type="radio"]:checked + span:before {
  content: " ";
  position: absolute;
  left: 50%;
  top: 50%;
  width: 15px;
  height: 11px;
  margin: -5px 0 0 -7px;
  background: url("../imgs/gou.jpg") no-repeat;
  background-size: 100% auto;
}
.pay-way {
  padding: 30px 20px;
}

.pay-way li {
  font-size: 14px;
}

.pay-way li label {
  display: block;
}

.pay-way li + li {
  margin-top: 25px;
}

.pay-way div {
  float: right;
}

.pay-way img {
  vertical-align: middle;
  margin: -3px 10px 0 0;
  width: 25px;
}

.pay-pop > p.low-versition-p {
  padding: 30px;
  text-align: center;
  font-size: 14px;
  border: none;
  color: #000;
}