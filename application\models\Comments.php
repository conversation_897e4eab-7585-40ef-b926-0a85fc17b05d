<?php

/**
 * class CommentsModel
 *
 * @property int $coid
 * @property int $cid
 * @property int $created
 * @property string $author
 * @property int $authorId
 * @property int $ownerId
 * @property string $mail
 * @property string $url
 * @property string $ip
 * @property int $app_aff
 * @property int $reply_aff
 * @property int $reply_author
 * @property string $agent
 * @property string $text utf7mb4
 * @property string $type
 * @property string $status
 * @property string $thumb
 * @property int $parent
 * @property int $admin_id
 * @property int $reply_ct
 * @property int $like_num
 * @property int $is_top
 * @property int $sec_parent 二级评论ID
 * @property int $fix_reply
 *
 * @property MemberModel $member app的用户
 * @property MemberModel $reply_member app的用户
 * @property ContentsModel $contents
 * @property ManagerModel $manager
 *
 * @mixin \Eloquent
 */
class CommentsModel extends BaseModel
{

    protected $table = "comments";

    protected $primaryKey = 'coid';

    protected $fillable
        = [
            'cid',
            'created',
            'author',
            'authorId',
            'ownerId',
            'mail',
            'app_aff',
            'reply_author',
            'reply_aff',
            'url',
            'ip',
            'agent',
            'text',
            'type',
            'via',
            'status',
            'parent',
            'thumb',
            'admin_id',
            'reply_ct',
            'like_num',
            'is_top',
            'sec_parent',
            'fix_reply',
        ];

    protected $guarded = 'coid';

    public $timestamps = false;

    const STATUS_APPROVED = 'approved';
    const STATUS_WAITING = 'waiting';
    const STATUS_SPAM = 'spam';
    const STATUS
        = [
            self::STATUS_APPROVED => 'approved',
            self::STATUS_WAITING  => 'waiting',
            self::STATUS_SPAM  => 'spam',
        ];

    const STATUS_TIPS = [
            self::STATUS_APPROVED => '通过',
            self::STATUS_WAITING  => '待审核',
            self::STATUS_SPAM  => '拒绝',
        ];

    const TOP_NO = 0;
    const TOP_OK = 1;
    const TOP_TIPS = [
        self::TOP_NO => '否',
        self::TOP_OK => '是',
    ];
    const VIA_CONTENTS = 1;
    const VIA_VIDEO = 2;
    const VIA_TIPS
        = [
            self::VIA_CONTENTS => '文章评论',
            self::VIA_VIDEO => '视频评论',
        ];
    const TYPE_COMMENT = 'comment';
    const TYPE
        = [
            self::TYPE_COMMENT => 'comment',
        ];

    protected $dateFormat = 'U';
    const UPDATED_AT = false;
    const CREATED_AT =  'created';
    protected $casts
        = [
            'created' => 'datetime:Y-m-d H:i:s'
        ];

    const SELECT_LIST_RAW = 'coid,cid,thumb,reply_author,author,authorId,ownerId,`text`,type,status,parent,created,reply_ct,like_num,is_top,sec_parent,app_aff,reply_aff';

    // 评论列表
    const CK_CON_COM_LIST = 'ck:con:com:list:%s:%s:%s:%s';
    const CK_CON_COM_FIRST_IDS_LIST = 'ck:con:com:first:ids:list:%s:%s';
    const CK_CON_FIRST_SEC_LIST = 'ck:con:first:sec:list:%s';
    const GP_CON_COM_LIST = 'gp:con:com:list';
    const COM_REPAIR_LIST = 'gp:com:repair:list1';
    const CN_CON_COM_LIST = '文章评论一级列表';

    // 评论列表
    const CK_CON_COM_REPLY_LIST = 'ck:con:com:reply:list:%s:%s:%s:%s';
    const GP_CON_COM_REPLY_LIST = 'gp:con:com:reply:list';
    const CN_CON_COM_REPLY_LIST = '文章评论二级列表';

    protected $appends = ['is_like'];

    public function member(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(MemberModel::class, 'aff', 'app_aff')->withDefault([
            'uid'      => 0,
            'aff'      => 0,
            'nickname' => '',
            'is_set_password' => 0,
            'new_user' => 0,
            'tag_list' => '',
            'vip_str' => '',
            'is_follow' => 0,
            'vip_bg' => '',
            'thumb_bg' => '',
            'is_official' => 0,
            'thumb' => '',
        ]);
    }

    public function reply_member(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(MemberModel::class, 'aff', 'reply_aff')->withDefault([
            'uid'      => 0,
            'aff'      => 0,
            'nickname' => '',
            'is_set_password' => 0,
            'new_user' => 0,
            'tag_list' => '',
            'vip_str' => '',
            'is_follow' => 0,
            'vip_bg' => '',
            'thumb_bg' => '',
            'is_official' => 0,
            'thumb' => '',
        ]);
    }

    public function contents(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(ContentsModel::class , 'cid' ,'cid');
    }

    public function reply(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(self::class , 'parent' , 'coid');
    }

    public function manager(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(ManagerModel::class , 'uid' ,'admin_id');
    }

    public function getThumbAttribute(): string
    {
        $thumb = $this->attributes['thumb'] ?? '';
        if (empty($thumb)){
            $thumb = DEFAULT_THUMB;
        }
        return url_image($thumb);
    }

    //是否点赞
    public function getIsLikeAttribute()
    {
        static $ary = null;
        if (APP_MODULE == 'staff') {
            return 1;
        }
        if (isset($this->attributes['is_like'])) {
            return $this->attributes['is_like'];
        }

        $aff = self::$watchUser ? self::$watchUser->aff : 0;
        if (empty($aff)) {
            return 0;
        }
        $coid = $this->attributes['coid'] ?? 0;

        $rk = sprintf(CommentsLikeModel::CONTENTS_COMMENTS_LIKE, $aff);
        if ($ary === null) {
            $ary = redis()->sMembers($rk);
        }
        if (empty($ary) || !is_array($ary) || !in_array($coid, $ary)) {
            return 0;
        }

        return 1;
    }

    public static function incrementLikeNum($coid, $num = 1)
    {
        return self::where('coid', $coid)->increment('like_num', $num);
    }

    public static function decrementLikeNum($coid, $num = 1)
    {
        return self::where('coid', $coid)->decrement('like_num', $num);
    }

    public static function list_first($cid,$via, $page, $limit){
        $key = sprintf(self::CK_CON_COM_LIST, $via,$cid, $page, $limit);
         return cached($key)
            ->group(self::GP_CON_COM_LIST)
            ->chinese(self::CN_CON_COM_LIST)
            ->fetchPhp(function () use ($cid, $page, $limit,$via){
                return self::where('cid', $cid)
                    ->with('member')
                    ->selectRaw(self::SELECT_LIST_RAW)
                    ->where('status', CommentsModel::STATUS_APPROVED)
                    ->where('parent', 0)
                    ->where('created', '<=', time())
                    ->orderByDesc('is_top')
                    ->orderByDesc('reply_ct')
                    ->orderByDesc('created')
                    ->forPage($page, $limit)
                    ->get()
                    ->each(function (CommentsModel $item) {
                        if ($item->app_aff == 0){
                            $item->member->nickname = $item->author;
                            $item->member->thumb = $item->thumb;
                            if ($item->is_top == CommentsModel::TOP_OK){
                                $item->member->is_official = 1;
                            }
                        }
                        //格式化a标签
                        if ($item->is_top == CommentsModel::TOP_OK){
                            $item->text = self::preg_match_a($item->text);
                        }
                    });
            }, rand(1800, 3600));
    }

    public static function fir_sec_comment($cid, $coid){
        $key = self::COM_REPAIR_LIST.":{$cid}:{$coid}";
        return cached($key)
            ->group(self::COM_REPAIR_LIST)
            ->chinese(self::CN_CON_COM_LIST)
            ->fetchPhp(function () use ($cid, $coid){
                return self::with(['member','reply_member'])
                    ->selectRaw(self::SELECT_LIST_RAW)
                    ->where('cid', $cid)
                    ->where('status', CommentsModel::STATUS_APPROVED)
                    ->where('parent', $coid)
                    ->orderByDesc('reply_ct')
                    ->limit(4)
                    ->get()->each(function (CommentsModel $item){
                        if ($item->app_aff == 0){
                            $item->member->nickname = $item->author;
                            $item->member->thumb = $item->thumb;
                        }
                        if (!$item->sec_parent){
                            $item->setRelation('reply_member', null);
                        }else{
                            if ($item->reply_aff == 0){
                                $item->reply_member->nickname = $item->reply_author;
                                $item->reply_member->thumb = $item->thumb;
                            }
                        }
                    });
            }, rand(1800, 3600));
    }


    public static function list_first_ids($cid,$via){
        $key = sprintf(self::CK_CON_COM_FIRST_IDS_LIST, $cid,$via);
        return cached($key)
            ->group(self::GP_CON_COM_LIST)
            ->chinese(self::CN_CON_COM_LIST)
            ->fetchJson(function () use ($cid,$via){
                return self::where('cid', $cid)
                    ->where('via', $via)
                    ->where('status', CommentsModel::STATUS_APPROVED)
                    ->where('parent', 0)
                    ->where('created', '<=', time())
                    ->orderByDesc('is_top')
                    ->orderByDesc('reply_ct')
                    ->orderByDesc('created')
                    ->limit(30)
                    ->get()
                    ->pluck('coid')
                    ->toArray();
            },1800);
    }

    public static function list_comments($cid,$via=1, $coids, $page, $limit){
        $key = sprintf(self::CK_CON_COM_LIST, $via,$cid, $page, $limit);
        return cached($key)
            ->group(self::GP_CON_COM_LIST)
            ->chinese(self::CN_CON_COM_LIST)
            ->fetchPhp(function () use ($cid, $coids, $page, $limit){
                return CommentsModel::where('cid', $cid)
                    ->with('member')
                    ->selectRaw(self::SELECT_LIST_RAW)
                    ->where('status', CommentsModel::STATUS_APPROVED)
                    ->whereNotIn('coid', $coids)
                    ->where('parent', 0)
                    ->where('created', '<=', time())
                    ->forPage($page - 1, $limit)
                    ->orderBy('created')
                    ->get()
                    ->each(function (CommentsModel $item) {
                        if ($item->app_aff == 0){
                            $item->member->nickname = $item->author;
                            $item->member->thumb = $item->thumb;
                            if ($item->is_top == CommentsModel::TOP_OK){
                                $item->member->is_official = 1;
                            }
                        }
                        $item->reply_member = null;
                    });
            },1800);
    }

    public static function list_replys($cid, $coid, $page, $limit){
        $key = sprintf(self::CK_CON_COM_REPLY_LIST, $cid, $coid, $page, $limit);
        return cached($key)
            ->group(self::GP_CON_COM_REPLY_LIST)
            ->chinese(self::CN_CON_COM_REPLY_LIST)
            ->fetchPhp(function () use ($cid, $coid, $page, $limit){
                return CommentsModel::where('cid', $cid)
                    ->with('member')
                    ->selectRaw(self::SELECT_LIST_RAW)
                    ->where('status', CommentsModel::STATUS_APPROVED)
                    ->where('parent', $coid)
                    ->orderBy('created')
                    ->forPage($page, $limit)
                    ->get()
                    ->each(function (CommentsModel $item) {
                        if ($item->app_aff == 0){
                            $item->member->nickname = $item->author;
                            $item->member->thumb = $item->thumb;
                        }
                        //回复
                        $tmp = null;
                        if ($item->sec_parent){
                            $reply_comment = self::where('coid', $item->sec_parent)
                                ->selectRaw(self::SELECT_LIST_RAW)
                                ->where('status', CommentsModel::STATUS_APPROVED)
                                ->first();
                            $tmp = [
                                'coid' => $reply_comment->coid,
                                'aff' => $reply_comment->app_aff,
                                'text' => $reply_comment->text,
                                'nickname' => $reply_comment->author,
                            ];
                        }
                        $item->setAttribute('reply', $tmp);
                        $item->setAttribute('is_owner', $item->authorId == $item->ownerId ? 1 : 0);
                    });
            },1800);
    }

    public static function preg_match_a($text){
        $reg1="/<a .*?>.*?<\/a>/";
        //这个存放的就是正则匹配出来的所有《a》标签数组
        preg_match_all($reg1, $text,$arr);
        collect($arr[0])->map(function ($item) use (&$text){
            $reg2="/href=\"([^\"]+)/";
            preg_match_all($reg2, $item,$href);
            //拿出《a》标签的内容
            $reg3="/>(.*)<\/a>/";
            preg_match_all($reg3, $item,$content);
            $text = str_replace($item, $content[1][0] . $href[1][0], $text);
        });
        return $text;
    }

}
