<?php

/**
 * class PlateModel
 * 
 * 
 * @property int $count_ct 数量
 * @property string $created_at 创建时间 
 * @property int $id  
 * @property string $name 板块名 
 * @property int $sort 排序 
 * @property int $status 状态 
 * @property int $type 1视频 2社区 
 * @property string $updated_at 修改时间
 * @property string $intro 简介
 * @property int $comment_ct 回复数
 *
 * 
 *
 * @mixin \Eloquent
 */
class PlateModel extends BaseModel
{
    protected $table = 'plate';
    protected $primaryKey = 'id';
    protected $fillable = ['count_ct', "p_id","bind_tags",'created_at', 'id', 'name',
        'sort', 'status', 'type', 'updated_at', 'intro',"is_hot", 'comment_ct','view_ct'];
    protected $guarded = 'id';
    public $timestamps = false;

    const STATUS_WAIT = 0;
    const STATUS_PASS = 1;
    const STATUS_OPTIONS = [
        self::STATUS_WAIT => '下架',
        self::STATUS_PASS => '上架'
    ];

    const HOT_YES = 1;
    const HOT_NO = 0;
    const HOT_OPTIONS = [
        self::HOT_NO => '非热门',
        self::HOT_YES => '热门'
    ];

    const CK_PLATE_LIST = 'ck:plate:list:%s';
    const GP_PLATE_LIST = 'gp:plate:list';
    const CK_PLATE_DETAIL = 'ck:plate:detail:%s';
    const GP_PLATE_DETAIL = 'gp:plate:detail';
    const CK_PLATE_FAVORITE_IDS = 'ck:plate:favorite:ids';

    protected $appends = ['status_str'];

    public function getStatusStrAttribute(): string
    {
        return self::STATUS_OPTIONS[$this->attributes['status']];
    }

    public static function queryBase(...$args)
    {
        return self::query()
            ->where('status', self::STATUS_PASS);
    }

    public static function listPlates($type = [],$pid=0)
    {
        if (APP_MODULE == "staff"){
            return self::when(!empty($type), function($q) use ($type) {
                    $q->whereIn('type', $type);
                })->when($pid >= 0, function($q) use ($pid) {
                    $q->where('p_id', $pid);
                })
                ->orderByDesc('sort')
                ->orderByDesc('id')
                ->select("id","name","status","type")
                ->get()->toArray();
        }else{
            $key = sprintf(self::CK_PLATE_LIST,$pid);
            return cached($key)
                ->group(self::GP_PLATE_LIST)
                ->fetchPhp(function() use($pid) {
                    return self::queryBase()
                        ->where('p_id', $pid)
                        ->orderByDesc('sort')
                        ->orderByDesc('id')
                        ->get(["id","name","status"])
                        ->map(function(\PlateModel $item) {
                            return $item;
                        });
                });
        }

    }

    public static function getDetail($id)
    {
        $key = sprintf(self::CK_PLATE_DETAIL, $id);
        return cached($key)
            ->group(self::GP_PLATE_DETAIL)
            ->fetchPhp(function() use($id) {
                return self::queryBase()
                    ->where('id', $id)
                    ->first();
            });
    }
}