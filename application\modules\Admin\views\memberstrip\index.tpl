{%include file="header.tpl"%}
<body>

<!-- 页面加载loading -->
<div class="page-loading">
    <div class="ball-loader">
        <span></span><span></span><span></span><span></span>
    </div>
</div>

<style>.layui-form.form-dialog .layui-input-block {
        margin-right: 30px
    }</style>
<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">管理</div>
                <div class="layui-form layui-card-header layuiadmin-card-header-auto">
                    <div class="layui-form-item">
                        <div class="layui-inline">
                            <label class="layui-form-label">ID</label>
                            <div class="layui-input-block">
                                <input type="text" name="where[id]" placeholder="请输入ID" autocomplete="off"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">aff</label>
                            <div class="layui-input-block">
                                <input type="text" name="like[aff]" placeholder="请输入aff"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">状态</label>
                            <div class="layui-input-block">
                                <select name="where[status]" id="">
                                    <option value="">全部</option>
                                    {%html_options options=MemberStripModel::STATUS_TIPS%}
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <label class="layui-form-label">收费类型</label>
                            <div class="layui-input-block">
                                <select name="where[pay_type]" id="">
                                    <option value="">全部</option>
                                    {%html_options options=MemberStripModel::PAY_TYPE_TIPS%}
                                </select>
                            </div>
                        </div>
                        <div class="layui-inline">
                            <button class="layui-btn layuiadmin-btn-useradmin" lay-submit lay-filter="search">
                                <i class="layui-icon layui-icon-search layuiadmin-button-btn"></i>
                            </button>
                        </div>
                    </div>
                </div>


                <div class="layui-card-body">
                    <table class="layui-table"
                           lay-data="{url:'{%url('listAjax')%}',page:true, id:'test',limit:20,limits:[10,20,30,40,50,60,70,80,90,1000],toolbar:'#toolbar'}"
                           lay-filter="table-toolbar">
                        <thead>
                        <tr>
                            <th lay-data="{type:'checkbox'}"></th>
                            <th lay-data="{field:'id',minWidth:100}">id</th>
                            <th lay-data="{field:'aff',minWidth:100}">aff</th>
                            <th lay-data="{minWidth:210,templet:'#attr3-1'}">处理前</th>
                            <th lay-data="{minWidth:210,templet:'#attr3-2'}">处理后</th>
                            <th lay-data="{field:'coins',minWidth:80,sort:true,edit:true}">金币</th>
                            <th lay-data="{field:'pay_type_str',minWidth:100}">收费类型</th>
                            <th lay-data="{field:'status_str',minWidth:80}">状态</th>
                            <th lay-data="{field:'reason',minWidth:80}">异常信息</th>
                            <th lay-data="{minWidth:170,templet:'#attr4'}">时间</th>
                            <th lay-data="{fixed: 'right',minWidth:240 ,align:'center', toolbar: '#operate-toolbar'}">操作</th>
                        </tr>
                        </thead>
                    </table>
                    <script type="text/html" id="attr3-1">
                        <div style="line-height: normal">
                            处理前:
                            <img style="display: inline-block;width: 115px;height: 115px;margin-bottom: 3px;"
                                 onclick="clickShowImage(this)" src="{{d.thumb}}">
                        </div>
                    </script>
                    <script type="text/html" id="attr3-2">
                        <div style="line-height: normal">
                            处理后:
                            <img style="display: inline-block;width: 115px;height: 115px;margin-bottom: 3px;"
                                 onclick="clickShowImage(this)" src="{{d.strip_thumb}}">
                        </div>
                    </script>
                    <script type="text/html" id="attr4">
                        创建&nbsp;{{d.created_at}}<br>
                        更新&nbsp;{{d.updated_at}}<br>
                    </script>

                    <script type="text/html" id="toolbar">
                        <div class="layui-btn-container">
                            <button class="layui-btn layui-btn-sm" lay-event="add">添加</button>
                            <button class="layui-btn layui-btn-sm" lay-event="delSelect" data-pk="id">删除所选</button>
                            <button class="layui-btn layui-btn-sm" lay-event="batchRetry" data-pk="id">处理所选</button>
                        </div>
                    </script>
                    <script type="text/html" id="operate-toolbar">
                        <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit">
                            <i class="layui-icon layui-icon-edit" ></i>修改</a>
                        <a class="layui-btn layui-btn-danger layui-btn-xs" data-pk="{{=d.id}}" lay-event="del">
                            <i class="layui-icon layui-icon-delete"></i>删除</a>
                        <a class="layui-btn layui-btn-danger layui-btn-xs" data-pk="{{=d.id}}" lay-event="retry">
                            <i class="layui-icon layui-icon-delete"></i>重试</a>
                    </script>
                </div>
            </div>
        </div>
    </div>
</div>

<script type="text/html" class="data-dialog" id="user-edit-dialog">
    <fieldset class="layui-elem-field layui-field-title" style="margin-top: 20px;">
        <legend>信息</legend>
    </fieldset>
    <form class="layui-form form-dialog" action="" lay-filter="form-save">
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">标题</label>
                <div class="layui-input-inline">
                    <input lay-verify="required" style="width: 838px" placeholder="标题" name="title" value="{{d.title || '' }}" class="layui-input">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">状态</label>
                <div class="layui-input-inline">
                    <select name="status" data-value="{{d.status || 0}}">
                        {%html_options options=FaceMaterialModel::STATUS_TIPS%}
                    </select>
                </div>
            </div>
            <div class="layui-inline">
                <label class="layui-form-label">收费类型</label>
                <div class="layui-input-inline">
                    <select name="status" data-value="{{d.type || 0}}">
                        {%html_options options=FaceMaterialModel::TYPE_TIPS%}
                    </select>
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">解锁金币</label>
                <div class="layui-input-inline">
                    <input lay-verify="required" placeholder="解锁金币" name="coins" value="{{d.coins || 0 }}"
                           class="layui-input">
                </div>
            </div>
        </div>
        <div class="layui-form-item">
            <div class="layui-inline">
                <label class="layui-form-label">素材图片：</label>
                <div class="layui-input-inline">
                    {%html_upload name='thumb' src='thumb' value='thumb'%}
                </div>
            </div>
        </div>
        <div class="layui-form-item layui-hide">
            <input type="hidden" name="_pk" value="{{=d.id}}">
            <button class="layui-btn submit" lay-submit="" lay-filter="save"></button>
        </div>
    </form>
</script>
{%include file="fooler.tpl"%}
<script>
    var fl = false;
    var gis_reload = false;
    window.reload_test = function (is_reload){
        gis_reload = is_reload
    }


    layui.use(['table', 'laytpl', 'form', 'lazy', 'laydate', 'layedit', 'upload', 'jquery'], function (table, laytpl, form, lazy, layDate, layEdit) {

        let verify = {}

            table.on('tool(table-toolbar)', function (obj) {
                //注tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
                var data = obj.data,
                    layEvent = obj.event,
                    that = this;
                switch (layEvent) {
                    case 'del':
                        layer.confirm('真的删除吗?', function (index) {
                            layer.close(index);
                            $.post("{%url('del')%}", {"_pk": $(that).data('pk')})
                                .then(function (json) {
                                    if (json.code) {
                                        Util.msgErr(json.msg);
                                    } else {
                                        Util.msgOk(json.msg);
                                        obj.del();
                                    }
                                })
                        });
                        break;
                    case 'retry':
                        layer.confirm('真的重新处理吗?', function (index) {
                            layer.close(index);
                            $.post("{%url('retry')%}", {"_pk": $(that).data('pk')})
                                .then(function (json) {
                                    if (json.code) {
                                        Util.msgErr(json.msg);
                                    } else {
                                        Util.msgOk(json.msg);
                                        obj.del();
                                    }
                                })
                        });
                        break;
                    case 'edit':
                        lazy('#user-edit-dialog')
                            .data(data)
                            .width("986px")
                            .dialog(function (id, ele) {
                                dialogCallback(id, ele, obj)
                            })
                            .laytpl(function () {
                                xx.renderSelect(data, $, form);
                                Util.uploader('button.but-upload-img', "{%url('upload/upload')%}", layui.upload, layui.jquery);
                                for (let i=0;i<=8;i++){
                                    $('#imgs-'+i+' img').on('load',function (){
                                        $('input[name="imgs['+i+'][w]"]').val(this.naturalWidth)
                                        $('input[name="imgs['+i+'][h]"]').val(this.naturalHeight)
                                    });
                                }
                                $('#videos-0 img').on('load',function (){
                                    $('input[name="videos[0][w]"]').val(this.naturalWidth)
                                    $('input[name="videos[0][h]"]').val(this.naturalHeight)
                                });
                            });
                        break;
                }
            })

        //监听头工具栏事件
        table.on('toolbar(table-toolbar)', function (obj) {
            var layEvent = obj.event;
            switch (layEvent) {
                case 'add':
                    lazy('#user-edit-dialog')
                        .width("986px")
                        .dialog(function (id, ele) {
                            dialogCallback(id, ele)
                        })
                        .laytpl(function () {
                            xx.renderSelect({}, $, form);
                            Util.uploader('button.but-upload-img', "{%url('upload/upload')%}", layui.upload, layui.jquery);
                            for (let i=0;i<=8;i++){
                                $('#imgs-'+i+' img').on('load',function (){
                                    $('input[name="imgs['+i+'][w]"]').val(this.naturalWidth)
                                    $('input[name="imgs['+i+'][h]"]').val(this.naturalHeight)
                                });
                            }
                            $('#videos-0 img').on('load',function (){
                                $('input[name="videos[0][w]"]').val(this.naturalWidth)
                                $('input[name="videos[0][h]"]').val(this.naturalHeight)
                            });
                        });
                    break;
                case 'refreshTime':
                    var checkStatus = table.checkStatus(obj.config.id),
                        data = checkStatus.data,
                        pkValAry = [],
                        pkName = $(this).data('pk');
                    for (var i = 0; i < data.length; i++) {
                        if (typeof (data[i][pkName]) !== "undefined") {
                            pkValAry.push(data[i][pkName])
                        }
                    }
                    if (pkValAry.length === 0) {
                        return Util.msgErr('请先选择行');
                    }
                    layer.confirm('确定执行操作吗?', function (index) {
                        layer.close(index);
                        $.post("{%url('refreshTime')%}", {"value": pkValAry.join(',')})
                            .then(function (json) {
                                if (json.code) {
                                    Util.msgErr(json.msg);
                                } else {
                                    Util.msgOk(json.msg);
                                    table.reload('test');
                                }
                            })
                    });
                    break;
                case 'clear':
                    layer.confirm('确定执行操作吗?', function (index) {
                        layer.close(index);
                        $.post("{%url('clearlist')%}", {"value": []})
                            .then(function (json) {
                                if (json.code) {
                                    Util.msgErr(json.msg);
                                } else {
                                    Util.msgOk(json.msg);
                                    table.reload('test');
                                }
                            })
                    });
                    break;
                case 'delSelect':
                    var checkStatus = table.checkStatus(obj.config.id),
                        data = checkStatus.data,
                        pkValAry = [],
                        pkName = $(this).data('pk');
                    for (var i = 0; i < data.length; i++) {
                        if (typeof (data[i][pkName]) !== "undefined") {
                            pkValAry.push(data[i][pkName])
                        }
                    }
                    if (pkValAry.length === 0) {
                        return Util.msgErr('请先选择行');
                    }
                    layer.confirm('真的删除吗?', function (index) {
                        layer.close(index);
                        $.post("{%url('delAll')%}", {"value": pkValAry.join(',')})
                            .then(function (json) {
                                if (json.code) {
                                    Util.msgErr(json.msg);
                                } else {
                                    Util.msgOk(json.msg);
                                    table.reload('test');
                                }
                            })
                    });
                    break;
                case 'batchRetry':
                    var checkStatus = table.checkStatus(obj.config.id),
                        data = checkStatus.data,
                        pkValAry = [],
                        pkName = $(this).data('pk');
                    for (var i = 0; i < data.length; i++) {
                        if (typeof (data[i][pkName]) !== "undefined") {
                            pkValAry.push(data[i][pkName])
                        }
                    }
                    if (pkValAry.length === 0) {
                        return Util.msgErr('请先选择行');
                    }
                    layer.confirm('重新进行脱衣处理吗?', function (index) {
                        layer.close(index);
                        $.post("{%url('retry')%}", {"_pk": pkValAry.join(',')})
                            .then(function (json) {
                                if (json.code) {
                                    Util.msgErr(json.msg);
                                } else {
                                    Util.msgOk(json.msg);
                                    table.reload('test');
                                }
                            })
                    });
                    break;
            }
        });

        table.on('edit(table-toolbar)',function(obj){
            let data = {'_pk': obj.data['id']};
            data[obj.field] = obj.value;
            $.post("{%url('save')%}", data).then(function (json) {
                layer.msg(json.msg);
            });
        });

        form.on('switch(status)', function (obj) {
            let id = this.id;
            let data = {'_pk': id};
            data[this.name] = this.checked ? 1 : 0;
            $.post("{%url('save')%}", data).then(function (json) {
                if (json.code) {
                    return Util.msgErr(json.msg);
                }else{
                    Util.msgOk('设置成功');
                }
            });
        });

        function dialogCallback(id, ele, obj) {
            let from = $(ele).find('form')
            $.post("{%url('save')%}", from.serializeArray())
                .then(function (json) {
                    layer.close(id);
                    if (json.code) {
                        return Util.msgErr(json.msg);
                    }
                    if (typeof (obj) == "undefined") {
                        //添加
                        Util.msgOk(json.msg);
                        table.reload('test')
                    } else {
                        //修改
                        obj.update(json.data);
                        Util.msgOk(json.msg);
                        table.reload('test')
                    }
                })
        }

        form.on('submit(search)', function (data) {
            var where = {}, ary = data.field, k;
            for (k in ary) {
                if (ary.hasOwnProperty(k) && ary[k].length > 0) {
                    if (k.substring(k.length - 4) === 'Time' && /^\d{4}-\d{2}-\d{2}$/.test(ary[k])) {
                        ary[k] += " 00:00:00";
                    }
                    where[k] = ary[k];
                } else {
                    where[k] = "__undefined__"
                }
            }
            table.reload('test', {
                where: where,
                page: {curr: 1}
            });
            return false;
        });

        //渲染日期
        $('.x-date-time').each(function (key, item) {
            layDate.render({elem: item, 'type': 'datetime'});
        });
        $('.x-date').each(function (key, item) {
            layDate.render({elem: item});
        });
        form.verify(verify);
        layEdit.set({uploadImage: {url: Util.config("editUpload", '')}});
    })
</script>