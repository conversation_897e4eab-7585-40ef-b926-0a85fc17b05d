<?php

/**
 * class AvActorsModel
 *
 * @property int $id
 * @property string $name av女优名
 * @property string $img 头像
 * @property int $video_num 视频数量
 * @property int $is_index 是否首页显示
 * @property int $hot_num 热度值
 *
 * <AUTHOR>
 * @date 2020-02-26 12:59:00
 *
 * @mixin \Eloquent
 */

class AvActorsModel extends BaseModel
{
    protected $table = "av_actors";

    protected $primaryKey = 'id';

    protected $fillable = ['id', 'name', 'img', 'video_num', 'hot_num', 'is_index', 'created_at', 'updated_at'];

    protected static $field = ['id', 'name', 'img', 'video_num', 'hot_num', 'is_index', 'created_at'];

    const ACTORS_MENU_LIST = [
        ['name' => '热度优先', 'key' => 'hot'],
        ['name' => '名称顺序', 'key' => 'name'],
        ['name' => '最近更新', 'key' => 'update'],
        ['name' => '最多影片', 'key' => 'video'],
    ];

    public function setImgAttribute($value)
    {
        parent::resetSetPathAttribute('img', $value);
    }

    public static function clearCache()
    {
        cached(self::LIST_KEY)
            ->clearGroup();
        self::allList();
    }

    const LIST_KEY = 'web:av:list:actors';
    public static function allList($isIndex=0, $orderByField='id', $imgRtn=true): array
    {
        $groupKey = self::LIST_KEY;
        $key      = self::LIST_KEY.":{$isIndex}_{$orderByField}";
        return cached($key)
            ->group($groupKey)
            ->chinese("av女优列表")
            ->fetchPhp(function () use ($isIndex, $orderByField, $imgRtn) {
                $where = [];
                if(!empty($isIndex) && $isIndex >= 0)
                {
                    $where['is_index'] = 1;
                }
                $result = self::where($where)
                    ->orderBy($orderByField, 'desc')
                    ->get(self::$field);
                $result = to_array($result);

                if($imgRtn === true && !empty($result))
                {
                    foreach ($result as &$v)
                    {
                        $v['cover'] = !empty($v['img']) ? url_image($v['img']) : '';
                        unset($v['img']);
                    }
                }


                return $result;
            }, 86400);
    }

    public static function getDetailByIds($ids): array
    {
        $list = self::allList();
        $rtn  = [];
        foreach ($list as $v)
        {
            if(in_array($v['id'], $ids))
            {
                array_push($rtn, $v);
            }
        }

        return $rtn;
    }

    public static function indexList()
    {
        $list = self::allList(1);
        $rtn  = [];
        foreach ($list as $v)
        {
            if($v['is_index'] == 1)
            {
                $v['img'] = !empty($v['img']) ? url_image($v['img']) : '';
                array_push($rtn, $v);
            }
        }

        return $rtn;
    }

    const DETAIL_KEY = 'web:av:detail:actorsv2:';

    public static function detail($id)
    {
        $key = self::DETAIL_KEY.$id;
        return cached($key)
            ->fetchPhp(function () use($id) {
                $result = self::where(['id' => $id])->first(self::$field);
                return !empty($result) ? $result->toArray() : [];
            });
    }
}