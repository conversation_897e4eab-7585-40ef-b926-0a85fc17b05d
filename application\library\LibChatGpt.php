<?php

use Hashids\Hashids;
/**
 * 功能描述：磁盘的检测，读取，以及文件目录的操作
 */
class LibChatGpt
{
//    protected static  $api_key = 'VkCmSZGHlalgnnAhtSnmKJWQeg667O8kgkuKq9o7';
    protected static  $api_key = 'J8bJPzdOodI4rSFxwmL9EawsYD46Xmd3JrRdAprq';
    protected static  $request_url = 'https://api.cohere.ai/v1/chat';

    private $salt = 'sdj23h~!@#uhfsweuse328'; // 加密算法
    private $len  = 10; // 加密算法

    private $hashids;
    public function __construct() {
        // 初始化 Hashids 实例
        if(empty($this->hashids))
        {
            $this->hashids = new Hashids($this->salt, $this->len);
        }
    }

    // 加密 ID
    public function encrypt($id): string
    {
        return $this->hashids->encode($id); // 生成加密字符串
    }

    // 解密 ID
    public function decrypt($hash) {
        $ids = $this->hashids->decode($hash); // 解码
        return $ids ? $ids[0] : null; // 返回解密后的 ID
    }

    public static function getCohereChatResponse($questionText)
    {
        $url = self::$request_url;

        // 设置请求数据
        $data = [
            'model' => 'command-r-plus',
            'message' => $questionText
        ];

        // 使用 cURL 发起 POST 请求
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Authorization: Bearer ' . self::$api_key,
            'Content-Type: application/json'
        ]);
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));

        // 执行请求并获取响应
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);

        $rtn = [];

        if ($httpCode === 200) {
            $rtn = json_decode($response, true);
        }

        return $rtn;
    }


}

?>
