<?php

/**
 * 订单统计上报处理
 * Class StatController
 */
class StatController extends \Yaf\Controller_Abstract
{

    private function sign(array $items)
    {
        ksort($items);
        unset($items['date']);
        $signkey = '132f1537f85scxpcm59f7e318b9epa51';
        $strings = '';
        foreach ($items as $key => $item) {
            $strings .= "{$key}={$item}&";
        }
        $strings = rtrim($strings, '&') . $signkey;
        return md5(hash('sha256', $strings));
    }

    private function coins_to_rmb_format($coins): string
    {
        return number_format($coins / WithdrawLogModel::COINS_RATIO, 2, '.', '');
    }
    /**
     * 内部细分上报统计
     */
    public function dataAction()
    {
        $data = $_GET;
        //var_dump($data);die();
        $s = $data['sign'];
        unset($data['sign'], $data['m'], $data['a']);
        $sign = $this->sign($data);
        if ($s != $sign) {
            echo json_encode(['code' => 0, 'msg' => 'access deny']);
            exit(0);
        }

        date_default_timezone_set('Asia/Shanghai');

        $start = date('Y-m-d 00:00:00', strtotime('-1 day'));
        $end   = date('Y-m-d 23:59:59', strtotime('-1 day'));

        $charge_and = OrdersModel::query()
            ->where('status', OrdersModel::PAY_STAT_SUCCESS)
            ->where('oauth_type', '=', 'android')
            ->where('updated_at', '>=', $start)
            ->where('updated_at', '<=', $end)->sum('pay_amount');

        $charge_ios = OrdersModel::query()
            ->where('status', OrdersModel::PAY_STAT_SUCCESS)
            ->where('oauth_type', '=', 'ios')
            ->where('updated_at', '>=', $start)
            ->where('updated_at', '<=', $end)->sum('pay_amount');

        $charge_pwa = OrdersModel::query()
            ->where('status', OrdersModel::PAY_STAT_SUCCESS)
            ->where('oauth_type', '=', 'web')
            ->where('updated_at', '>=', $start)
            ->where('updated_at', '<=', $end)->sum('pay_amount');

        $ai_strip_consumption = MemberStripModel::query()
            ->where('created_at', '>=', $start)
            ->where('created_at', '<=', $end)
            ->where('pay_type', '=', MemberStripModel::PAY_TYPE_COINS)
            ->sum('coins');

        $ai_strip_ct = MemberStripModel::query()
            ->where('created_at', '>=', $start)
            ->where('created_at', '<=', $end)
            ->count();

        $ai_image_change_face_consumption = MemberFaceModel::query()
            ->where('created_at', '>=', $start)
            ->where('created_at', '<=', $end)
            ->where('pay_type', '=', MemberStripModel::PAY_TYPE_COINS)
            ->sum('coins');

        $ai_image_change_face_ct = MemberFaceModel::query()
            ->where('created_at', '>=', $start)
            ->where('created_at', '<=', $end)
            ->count();

        $ai_draw_consumption              = 0;
        $ai_draw_ct                       = 0;
        $ai_image_to_video_consumption    = 0;
        $ai_image_to_video_ct             = 0;
        $ai_video_change_face_consumption = 0;
        $ai_video_change_face_ct          = 0;
        $ai_girlfriend_consumption        = 0;
        $ai_girlfriend_ct                 = 0;

        $date = date('Y-m-d', strtotime('-1 days'));
        $data = [
            'code' => 1,
            'data' => [
                'reg_and'                 => SysTotalModel::getValueBy('member:active:and', $date),
                'reg_ios'                 => SysTotalModel::getValueBy('member:active:ios', $date),
                'reg_pwa'                 => SysTotalModel::getValueBy('member:active:web', $date),
                'charge_and'              => intval($charge_and / 100),
                'charge_ios'              => intval($charge_ios / 100),
                'charge_pwa'              => intval($charge_pwa / 100),
                'activity'                => SysTotalModel::getValueBy('member:active', $date),
                'ai_draw'                 => $this->coins_to_rmb_format($ai_draw_consumption),
                'ai_draw_ct'              => $ai_draw_ct,
                'ai_image_to_video'       => $this->coins_to_rmb_format($ai_image_to_video_consumption),
                'ai_image_to_video_ct'    => $ai_image_to_video_ct,
                'ai_strip'                => $this->coins_to_rmb_format($ai_strip_consumption),
                'ai_strip_ct'             => $ai_strip_ct,
                'ai_image_change_face'    => $this->coins_to_rmb_format($ai_image_change_face_consumption),
                'ai_image_change_face_ct' => $ai_image_change_face_ct,
                'ai_video_change_face'    => $this->coins_to_rmb_format($ai_video_change_face_consumption),
                'ai_video_change_face_ct' => $ai_video_change_face_ct,
                'ai_girlfriend'           => $this->coins_to_rmb_format($ai_girlfriend_consumption),
                'ai_girlfriend_ct'        => $ai_girlfriend_ct,
            ]
        ];
        echo json_encode($data);
    }
    public function reportAction()
    {
        $data = $_GET;
        //var_dump($data);die();
        $s = $data['sign'];
        unset($data['sign'], $data['m'], $data['a']);
        $sign = $this->sign($data);
        if ($s != $sign) {
            echo json_encode(['code' => 0, 'msg' => 'access deny']);exit(0);
        }
        date_default_timezone_set('Asia/Shanghai');
        if (!isset($_GET['date'])) {
            $date = date('Y-m-d', strtotime('-1 days'));
        } else {
            $date = $_GET['date'];
        }
        $start = "$date 00:00:00";
        $end = "$date 23:59:59";
        $monthStart = date('Y-m-01 00:00:00', strtotime('-1 day'));

        $order = OrdersModel::query()
            ->where('status', OrdersModel::PAY_STAT_SUCCESS)
            ->whereBetween('updated_at' , [$start, $end]);

        $month = OrdersModel::query()
            ->where('status', OrdersModel::PAY_STAT_SUCCESS)
            ->where('updated_at', '>=', $monthStart)
            ->sum('pay_amount');


        $total = MemberLogModel::max('id');
        $count = $order->count();
        $amount = $order->sum('pay_amount');

        //game
        $game_amount = 0;
        $game_draw_amount = 0;
        $game_trans_amount = 0;
        $data = [
            'code' => 1,
            'data' => [
                'activity'          => SysTotalModel::getValueBy('member:active',$date),
                'reg'               => SysTotalModel::getValueBy('member:create',$date),
                'total'             => $total,
                'amount'            => intval($amount / 100),
                'count'             => $count,
                'month'             => intval($month / 100),
                'game_amount'       => intval($game_amount / 100),
                'game_draw_amount'  => intval($game_draw_amount),
                'game_trans_amount' => intval($game_trans_amount)-intval($game_amount / 100),
            ]
        ];
        echo json_encode($data);
    }
}