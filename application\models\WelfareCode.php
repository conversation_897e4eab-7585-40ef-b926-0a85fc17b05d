<?php

/**
 * class WelfareCodeModel
 *
 * @property int $id
 * @property int $product_id 产品ID
 * @property string $code 兑换码
 * @property int $status 状态
 * @property int $created_at 创建时间
 * @property int $updated_at 更新时间
 * @mixin \Eloquent
 */
class WelfareCodeModel extends BaseModel
{
    protected $table = 'welfare_code';
    protected $primaryKey = 'id';
    protected $fillable = [
        'product_id',
        'code',
        'status',
        'created_at',
        'updated_at',
    ];

    const CK_WELFARECODE_LIST = 'ck:welfarecode:list';
    const GP_WELFARECODE_LIST = 'gp:welfarecode:list';
    const CN_WELFARECODE_LIST = '福利兑换码列表';

    const STATUS_0 = 0;
    const STATUS_1 = 1;
    const STATUS_TIPS = [
        self::STATUS_0 => '未兑换',
        self::STATUS_1  => '已兑换',
    ];
    const PRODUCT_OPTIONS = [
        ["id"=>1,"name"=>"vip%s天","value"=>3,"type"=>1],
        ["id"=>2,"name"=>"ai次%s次","value"=>10,"type"=>2],
        ["id"=>3,"name"=>"金币%s个","value"=>3,"type"=>3],

    ];

    public static function listWelfareCodes()
    {
        return cached(self::CK_WELFARECODE_LIST)
            ->group(self::GP_WELFARECODE_LIST)
            ->chinese(self::CN_WELFARECODE_LIST)
            ->fetchPhp(function () {
                return self::query()
                    //->pluck('code', 'catagory_id')
                        ->get()
                    ->toArray();
            });
    }
}