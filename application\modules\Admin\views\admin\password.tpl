<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>设置我的密码</title>
    <meta name="renderer" content="webkit">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
    <meta name="viewport"
          content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=1.0, user-scalable=0">
    <link rel="stylesheet" href="<?php echo LAY_UI_STATIC ?>layuiadmin/layui/css/layui.css" media="all">
    <link rel="stylesheet" href="<?php echo LAY_UI_STATIC ?>layuiadmin/style/admin.css" media="all">
</head>
<body>

<div class="layui-fluid">
    <div class="layui-row layui-col-space15">
        <div class="layui-col-md12">
            <div class="layui-card">
                <div class="layui-card-header">修改密码</div>
                <div class="layui-card-body" pad15>

                    <div class="layui-form" lay-filter="">
                        <div class="layui-form-item">
                            <label class="layui-form-label">当前密码</label>
                            <div class="layui-input-inline">
                                <input type="password" name="old_pwd" lay-verify="required" lay-verType="tips"
                                       class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">新密码</label>
                            <div class="layui-input-inline">
                                <input type="password" name="password" lay-verify="required|pass" lay-verType="tips"
                                       autocomplete="off" id="LAY_password" class="layui-input">
                            </div>
                            <div class="layui-form-mid layui-word-aux">6到16个字符</div>
                        </div>
                        <div class="layui-form-item">
                            <label class="layui-form-label">确认新密码</label>
                            <div class="layui-input-inline">
                                <input type="password" name="re_pass" lay-verify="required|re_pass" lay-verType="tips"
                                       autocomplete="off" class="layui-input">
                            </div>
                        </div>
                        <div class="layui-form-item">
                            <div class="layui-input-block">
                                <button class="layui-btn" lay-submit lay-filter="save">确认修改</button>
                            </div>
                        </div>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

<script src="<?php echo LAY_UI_STATIC ?>layuiadmin/layui/layui.js"></script>
<script src="<?php echo LAY_UI_STATIC ?>util.js"></script>
<script>
    layui.config({
        base: '<?php echo LAY_UI_STATIC ?>layuiadmin/' //静态资源所在路径
    }).extend({
        index: 'lib/index' //主入口模块
    }).use(['index']);
    layui.use(['HTTPUtil', 'element', 'jquery', 'form', 'upload'], function () {
        var form = layui.form,
            $ = layui.jquery,
            ajax = layui.HTTPUtil,
            verify = {};
        form.verify({
            "pass": [
                /^[\S]{6,18}$/
                , '密码必须6到18位，且不能出现空格'
            ],
            "re_pass": function (val) {
                if ($("#LAY_password").val() !== val) {
                    return '两次密码不一致';
                }
            }

        });

        form.on('submit(save)', function (data) {
            ajax.post(location.href, data.field).then(function (json) {
                if (json.code !== 0) {
                    Util.msgErr(json.msg);
                } else {
                    Util.msgOk(json.msg, Util.reload);
                }
            });
            return false;
        });


    });


</script>
</body>
</html>