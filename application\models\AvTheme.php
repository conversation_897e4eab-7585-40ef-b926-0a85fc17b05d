<?php

/**
 * class AvThemeModel
 *
 * @property int $id
 * @property string $name 主题名
 * @property string $img 主题背景图
 * @property int $video_num 视频数量
 * @property int $is_index 是否首页显示
 * <AUTHOR>
 * @date 2020-02-26 12:59:00
 *
 * @mixin \Eloquent
 */

class AvThemeModel extends BaseModel
{
    protected $table = "av_theme";

    protected $primaryKey = 'id';

    protected $fillable = ['id', 'name', 'img', 'video_num', 'is_index', 'created_at', 'updated_at'];

    protected static $field = ['id', 'name', 'img', 'video_num', 'is_index', 'created_at'];

    public function setImgAttribute($value)
    {
        parent::resetSetPathAttribute('img', $value);
    }

    const LIST_KEY = 'web:av:list:theme';

    public static function allList($isIndex='-1')
    {
        $key = self::LIST_KEY."_{$isIndex}";
        return cached($key)
            ->group(self::LIST_KEY)
            ->chinese("av主题列表")
            ->fetchPhp(function () use($isIndex){
                $model  = self::select(self::$field);
                if($isIndex >= 0)
                {
                    $model->where(['is_index' => $isIndex]);
                }

                $result = $model->get();
                return to_array($result);
            }, 86400);
    }

    const DETAIL_KEY = 'web:av:detail:themes:';

    public static function detail($id)
    {
        $key = self::DETAIL_KEY.$id;
        return cached($key)
            ->fetchPhp(function () use($id) {
                $result = self::where(['id' => $id])->first(self::$field);
                return !empty($result) ? $result->toArray() : [];
            });
    }

    public static function clearCache()
    {
        cached(self::LIST_KEY)
            ->clearGroup();
        self::allList();
    }

}