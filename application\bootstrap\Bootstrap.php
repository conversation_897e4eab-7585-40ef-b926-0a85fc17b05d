<?php

use Yaf\Dispatcher;
use Yaf\Application;
use Yaf\Bootstrap_Abstract;
use \Illuminate\Database\Capsule\Manager;
use Yaf\Registry;

class Bootstrap extends Bootstrap_Abstract
{
    private $config;
    private $database;

    public function _initLoader(Dispatcher $dispatcher)
    {
    }

    public function _initConfig()
    {
        $this->config = Application::app()->getConfig();
        Registry::set('config', $this->config);
    }

    public function _initOne(Dispatcher $dispatcher)
    {
        Yaf\Loader::import('function/common.php');
        Yaf\Loader::import('function/helper.php');
        Yaf\Loader::import('function/init.php');
        $dispatcher->disableView();
    }

    public static function error_handError($errno, $errStr, $errFile, $errLine)
    {
        if ($errno == E_WARNING) {
            if (strpos($errStr, 'Smarty/Internal') !== false) {
                return true;
            }
            if (strpos($errStr, 'Symfony/Component/Translation') !== false) {
                return true;
            }
        }
        $error = '['.date('Y-m-d H:i:s').']'."\r\n";
        $excep = new Exception($errStr);
        $error .= '  错误级别：'.$errno."\r\n";
        $error .= '  错误信息：'.$errStr."\r\n";
        $error .= '  错误文件：'.$errFile."\r\n";
        $error .= '  错误行数：'.$errLine."\r\n";
        $error .= "\r\n";
        error_log($error, 3, APP_PATH.'/storage/logs/log.log');
        trigger_log($excep);

        return false;
    }

    public function _initErrorHandle(Dispatcher $dispatcher)
    {
        $dispatcher->setErrorHandler([self::class, 'error_handError']);
        $dispatcher->catchException(true);
        $dispatcher->throwException(true);
    }

    public function _initDefaultDbAdapter()
    {
        try {
            $object = new \Yaf\Config\Ini(APP_PATH.'/conf/database.ini', ini_get('yaf.environ'));
            if ($object->es) {
                class_alias(tools\Elasticsearch::class, '\LibEs');
                \LibEs::registerConfig([$object->es->toArray()]);
            }
        } catch (\Throwable $e) {
            $object = $this->config;
        }
        Registry::set('redis', $object->redis);
        Registry::set('database' , $object->database);

        $capsule            = new Manager;
        $config             = $object->database->toArray();
        $config['timezone'] = '+8:00';
        if (isset($config['read'])){
            $read = explode(',', $config['read']['host']);
            if (count($read) > 1) {
                $config['read']['host'] = $read;
            }
        }

        // 加载自定义的MySqlConnector来修复缓冲查询问题
        require_once APP_PATH . '/application/library/Illuminate/Database/Connectors/MySqlConnector.php';
        error_log("Custom MySqlConnector loaded successfully");
        
        // 修复MySQL PDO问题 - 使用最兼容的配置
        $config['driver'] = 'mysql';
        $config['strict'] = false;
        $config['engine'] = null;
        $config['collation'] = $config['collation'] ?? 'utf8_unicode_ci';

        // 强制设置连接参数
        $config['database'] = $config['database'] ?? 'yaf_hlcg';
        $config['host'] = $config['read']['host'] ?? $config['host'] ?? 'db';
        $config['port'] = $config['port'] ?? '3306';

        // 添加SSL禁用配置
        $config['sslmode'] = 'DISABLED';
        $config['ssl'] = false;

        // 强制设置PDO选项 - 修复 "Cannot execute queries while other unbuffered queries are active" 错误
        $config['options'] = [
            PDO::ATTR_PERSISTENT => false,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . $config['charset'],
            PDO::ATTR_EMULATE_PREPARES => true,
            PDO::ATTR_TIMEOUT => 30,
            // 关键修复：强制启用缓冲查询
            PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
            PDO::ATTR_STRINGIFY_FETCHES => false,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_FOUND_ROWS => true,
            PDO::MYSQL_ATTR_LOCAL_INFILE => false,
            // 添加额外的MySQL特定选项来确保连接稳定性
            PDO::MYSQL_ATTR_MULTI_STATEMENTS => false,
            PDO::ATTR_AUTOCOMMIT => true
        ];

        // 不设置unix_socket，让它保持未定义状态，这样自定义连接器就会使用TCP连接

        // 添加数据库特定配置
        $config['fetch'] = PDO::FETCH_OBJ;
        $config['sticky'] = true;
        
        $capsule->addConnection($config, 'default');
        
        // 通用PDO选项配置 - 用于所有数据库连接
        $commonPdoOptions = [
            PDO::ATTR_PERSISTENT => false,
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_EMULATE_PREPARES => true,
            PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::MYSQL_ATTR_MULTI_STATEMENTS => false,
            PDO::ATTR_AUTOCOMMIT => true,
            PDO::MYSQL_ATTR_LOCAL_INFILE => false
        ];

        // 添加manticore连接
        if ($object->manticore){
            $manticore = [
                'driver'   => 'mysql',
                'host'     => $object->manticore->host,
                'port'     => $object->manticore->port,
                'database' => null,
                'sslmode'  => 'DISABLED',
                'ssl'      => false,
                'options'  => $commonPdoOptions,
            ];
            $capsule->addConnection($manticore, 'manticore');
        }

        // 添加其他数据库连接（im、mhsync、tbr）
        $additionalConnections = ['im', 'mhsync', 'tbr'];
        foreach ($additionalConnections as $connectionName) {
            if (isset($object->$connectionName)) {
                $connectionConfig = $object->$connectionName->toArray();
                $additionalConfig = [
                    'driver' => 'mysql',
                    'host' => $connectionConfig['read']['host'] ?? $connectionConfig['host'] ?? 'localhost',
                    'port' => $connectionConfig['port'] ?? '3306',
                    'database' => $connectionConfig['database'],
                    'username' => $connectionConfig['username'],
                    'password' => $connectionConfig['password'],
                    'charset' => $connectionConfig['charset'] ?? 'utf8',
                    'collation' => $connectionConfig['collation'] ?? 'utf8_unicode_ci',
                    'prefix' => $connectionConfig['prefix'] ?? '',
                    'strict' => false,
                    'sslmode' => 'DISABLED',
                    'ssl' => false,
                    'options' => array_merge($commonPdoOptions, [
                        PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . ($connectionConfig['charset'] ?? 'utf8') . "; SET sql_mode='';"
                    ])
                ];
                $capsule->addConnection($additionalConfig, $connectionName);
            }
        }
        
        // 设置全局配置
        $capsule->setAsGlobal();
        $capsule->bootEloquent();
        
        // 强制设置所有连接的PDO属性来解决缓冲查询问题
        $connectionNames = ['default', 'manticore', 'im', 'mhsync', 'tbr'];
        foreach ($connectionNames as $connectionName) {
            try {
                $connection = $capsule->getConnection($connectionName);
                if ($connection) {
                    // 延迟获取PDO连接，避免在初始化时立即建立连接
                    $connection->listen(function ($query) use ($connectionName) {
                        // 连接建立后的回调，确保PDO属性正确设置
                        static $initialized = [];
                        if (!isset($initialized[$connectionName])) {
                            try {
                                $pdo = $query->connection->getPdo();
                                if ($pdo) {
                                    $pdo->setAttribute(PDO::MYSQL_ATTR_USE_BUFFERED_QUERY, true);
                                    $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, true);
                                    $initialized[$connectionName] = true;
                                }
                            } catch (Exception $e) {
                                error_log("设置{$connectionName}连接PDO属性失败: " . $e->getMessage());
                            }
                        }
                    });
                }
            } catch (Exception $e) {
                // 连接不存在时忽略错误
                if (strpos($e->getMessage(), 'not configured') === false) {
                    error_log("配置{$connectionName}连接失败: " . $e->getMessage());
                }
            }
        }
        
        class_alias(Manager::class, 'DB');
    }

    public function _initViews(Dispatcher $dispatcher)
    {
        if (APP_MODULE === 'staff') {
            $smarty = new Smarty\adapter(null,
                $this->config->smarty->toArray());
            $dispatcher->setView($smarty);
        }
    }

    // 初始化用户信息
    public function _initPlugins(Dispatcher $dispatcher)
    {
        $dispatcher->registerPlugin(new RouterPlugin());
    }
}