<?php


namespace service;

class AppService
{
    public function listCategories(\MemberModel $member)
    {
        if (CommonService::isPcQuest($member->oauth_type)){
            return \PcAppCategoryModel::listCategories();
        }else{
            return \AppCategoryModel::listCategories();
        }
    }

    public function listApps(\MemberModel $member, $id, $page, $limit)
    {
        if (CommonService::isPcQuest($member->oauth_type)){
            return \PcAppModel::listApps($id, $page, $limit);
        }else{
            return \AppModel::listApps($id, $page, $limit);
        }
    }

    public function categoryApps(\MemberModel $member)
    {
        if (CommonService::isPcQuest($member->oauth_type)){
            return \PcAppModel::categoryApps();
        }else{
            return \AppModel::categoryApps();
        }
    }
}