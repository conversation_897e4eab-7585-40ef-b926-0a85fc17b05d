<?php

use Illuminate\Support\Str;
use service\UserService;
use Carbon\Carbon;
use service\ChannelService;

class LibVideo
{
    const REMOTE_CONSTRUCT_URL = 'http://zpc.we-cname.com/index.php/sync/construct';

    const REMOTE_ELEMENT_URL = 'http://zpc.we-cname.com/index.php/sync/element';

    const REMOTE_ELEMENT_VALUE_URL = 'http://zpc.we-cname.com/index.php/sync/element_value';

    public static function syncConstruct()
    {
//        $lock = redis()->get(self::CK_LOCK_CONSTRUCT);
//        if ($lock) {
//            return;
//        }
//        redis()->setnxttl(self::CK_LOCK_CONSTRUCT, 1, 1200);
        redis()->del(self::RK_REMOTE_CONSTRUCT);
        try {
            // 处理更新
            $id = 0;
            while (true) {
                $params = [
                    'app_name' => config('pay.app_name'),
                    'time'     => time(),
                    'node_id'  => $id,
                    'limit'    => 100,
                ];
                $params['sign'] = LibCrypt::make_sign($params, self::SIGN_KEY);
                $data = self::req(self::REMOTE_CONSTRUCT_URL, $params);
                $data = $data['data'] ?? [];
                if (empty($data)) {
                    break;
                }

                foreach ($data as $item) {
                    $item = self::del_extra($item, self::SYNC_CONSTRUCT_FIELDS);

                    redis()->sAdd(self::RK_REMOTE_CONSTRUCT, $item['id']);
                    $record = VideoConstructModel::where('id', $item['id'])->first();

                    if (empty($record)) {
                        VideoConstructModel::create($item);
                        trigger_log(" cate ... 1");
                    }else{
                        self::update_construct_record($record, $item);
                    }
                }

                trigger_log(" cate ... 2");
                $id = $data[count($data) - 1]['id'];
            }

            // 处理删除
            self::defend_construct_del();
            trigger_log(" cate ... 3");
        } catch (Throwable $e) {
            trigger_log(' construct ... 出现异常'.$e->getMessage());
        }

//        redis()->del(self::CK_LOCK_CONSTRUCT);
    }

    protected static function update_construct_record($record, $data)
    {
        $fields = ['status', 'bind_type', 'bind_tags', 'type', 'api', 'params'];
        list($change, $tmp_data) = self::change_data($fields, $record, $data);
        if (!$change) {
            return;
        }
        $record->fill($tmp_data);
        $isOk = $record->save();
        test_assert($isOk, '无法更新结构数据');
    }

    protected static function defend_construct_del()
    {
        VideoConstructModel::select(['id'])
            ->chunk(1000, function ($items) {
                collect($items)->map(function ($item) {
                    $exists = redis()->sismember(self::RK_REMOTE_CONSTRUCT, $item->id);
                    if ($exists) {
                        return;
                    }
                    $isOk = $item->delete();
                    test_assert($isOk, '删除结构记录异常');
                });
            });
    }

    protected static function update_element_record($record, $data)
    {
        $fields = ['adm_title', 'title', 'sub_title', 'icon', 'construct_id', 'type', 'content_type', 'sub_content_type', 'sub_field', 'sub_maxnum', 'banner_scale', 'more_button', 'more_api', 'more_api_params', 'more_page_show_type', 'max_num', 'show_field', 'change_button', 'is_margin', 'status', 'sort'];
        list($change, $tmp_data) = self::change_data($fields, $record, $data);
        if (!$change) {
            return;
        }

        $record->fill($tmp_data);
        $isOk = $record->save();
        test_assert($isOk, '无法更新结构数据');
    }

    protected static function defend_element_del()
    {
        VideoElementModel::select(['id'])
            ->chunk(1000, function ($items) {
                collect($items)->map(function ($item) {
                    $exists = redis()->sismember(self::RK_REMOTE_ELEMENT, $item->id);
                    if ($exists) {
                        return;
                    }
                    $isOk = $item->delete();
                    test_assert($isOk, '删除元素记录异常');
                });
            });
    }

    public static function syncElement()
    {
//        $lock = redis()->get(self::CK_LOCK_ELEMENT);
//        if ($lock) {
//            return;
//        }
//        redis()->setnxttl(self::CK_LOCK_ELEMENT, 1, 1200);
//        redis()->del(self::RK_REMOTE_ELEMENT);
        try {
            // 处理更新
            $id = 0;
            while (true) {
                $params = [
                    'app_name' => config('pay.app_name'),
                    'time'     => time(),
                    'node_id'  => $id,
                    'limit'    => 100,
                ];
                $params['sign'] = LibCrypt::make_sign($params, self::SIGN_KEY);
                $data = self::req(self::REMOTE_ELEMENT_URL, $params);
                $data = $data['data'] ?? [];
                if (empty($data)) {
                    break;
                }

                trigger_log(" video element 1 ");
                collect($data)->map(function ($item) {
                    $item = self::del_extra($item, self::SYNC_ELEMENT_FIELDS);
                    redis()->sAdd(self::RK_REMOTE_ELEMENT, $item['id']);
                    $record = VideoElementModel::where('id', $item['id'])->first();
                    trigger_log(" video element 2 ".var_export($item, true));
                    if (empty($record)) {
                        $isOk = VideoElementModel::create($item);
                    }else{
                        self::update_element_record($record, $item);
                    }

                    trigger_log(" video element 3 ");
                });
                $id = $data[count($data) - 1]['id'];
            }

            // 处理删除
            self::defend_element_del();
            trigger_log(" video element 4 ");

        } catch (Throwable $e) {
            trigger_log('出现异常'.$e->getMessage());
        }
//        redis()->del(self::CK_LOCK_ELEMENT);
    }

    protected static function update_element_value_record($record, $data)
    {
        $fields = ['related_id', 'element_id', 'link_url', 'resource_url', 'redirect_type', 'router', 'desc', 'open_type'];
        list($change, $tmp_data) = self::change_data($fields, $record, $data);
        if (!$change) {
            return;
        }
        $record->fill($tmp_data);
        $isOk = $record->save();
        test_assert($isOk, '无法更新结构数据');
    }

    protected static function defend_element_value_del()
    {
        VideoElementValModel::select(['id', 'f_id'])
            ->where('f_id', 'like', self::MARK . '%')
            ->chunk(1000, function ($items) {
                collect($items)->map(function ($item) {
                    $f_id = str_replace(self::MARK, '', $item->f_id);
                    $exists = redis()->sismember(self::RK_REMOTE_ELEMENT_VALUE, $f_id);
                    if ($exists) {
                        return;
                    }
                    $isOk = $item->delete();
                    test_assert($isOk, '删除元素value记录异常');
                });
            });
    }

    public static function syncElementValue()
    {
//        $lock = redis()->get(self::CK_LOCK_ELEMENT_VALUE);
//        if ($lock) {
//            return;
//        }
//        redis()->setnxttl(self::CK_LOCK_ELEMENT_VALUE, 1, 1200);
        redis()->del(self::RK_REMOTE_ELEMENT_VALUE);
        try {
            // 处理更新
            $id = 0;
            while (true) {
                $params = [
                    'app_name' => config('pay.app_name'),
                    'time'     => time(),
                    'node_id'  => $id,
                    'limit'    => 100,
                ];
                $params['sign'] = LibCrypt::make_sign($params, self::SIGN_KEY);
                $data = self::req(self::REMOTE_ELEMENT_VALUE_URL, $params);
                $data = $data['data'] ?? [];
                if (empty($data)) {
                    break;
                }

                collect($data)->map(function ($item) {
                    $item = self::del_extra($item, self::SYNC_ELEMENT_VALUE_FIELDS);
                    redis()->sAdd(self::RK_REMOTE_ELEMENT_VALUE, $item['id']);
                    $f_id = self::MARK . $item['id'];
                    $record = VideoElementValModel::where('f_id', $f_id)->first();
                    if (empty($record)) {
                        $item['f_id'] = $f_id;
                        unset($item['id']);
                        VideoElementValModel::create($item);
                    }else{
                        self::update_element_value_record($record, $item);
                    }
                });
                $id = $data[count($data) - 1]['id'];
            }

            // 处理删除
            self::defend_element_value_del();

        } catch (Throwable $e) {
            trigger_log('出现异常'.$e->getMessage());
        }
//        redis()->del(self::CK_LOCK_ELEMENT_VALUE);
    }

    public static function syncVideo()
    {
        ini_set('memory_limit', '-1');
//        self::syncParentCate();
//        self::syncCateTwo();
        self::syncVideoHandle();

    }

    private static function getRandPlateId()
    {
        $ids = cached("rand_plate")->fetchPhp(function(){
            $plateList = PlateModel::where(['p_id' => 0])->select(['id', 'status'])->get();
            $plateList = to_array($plateList);
            return array_column($plateList, 'id');
        });

        return $ids[array_rand($ids)];
    }

    private static function syncVideoHandle()
    {
        try {
            // 处理更新
            $id = 0;
            while (true) {
                $params = [
                    'app_name'  => config('pay.app_name'),
                    'time'      => time(),
                    'node_time' => '',
                    'node_id'   => $id,
                    'limit'     => 1000,
                ];
                $params['sign'] = LibCrypt::make_sign($params, self::SIGN_KEY);
                $data = self::req(self::REMOTE_MV_URL, $params);
                $data = $data['data'] ?? [];
                if (empty($data)) {
                    break;
                }

                foreach ($data as $v)
                {
                    $vDetail = VideosModel::where(['_id' => $v['_id']])->first();
                    if(!empty($vDetail))
                    {
                        continue;
                    }

                    ///new/av
                    if(strpos($v['cover_horizontal'], '.av/') !== false
                    || strpos($v['cover_horizontal'], 'new/av') !== false)
                    {
                        continue;
                    }

                    $cateId = $v['construct_id'] ?? 0;
                    if(!empty($cateId))
                    {
                        $detail = PlateModel::where(['vid' => $cateId])->first(['id', 'vid', 'status']);
                        $pId    = $detail['id'] ?? 0;
                    }
                    else
                    {
                        $pId = self::getRandPlateId();
                    }

                    $videoId = VideosModel::insertGetId([
                        "title"        => $v["title"],
                        "_id"          => $v["_id"],
                        "_id_hash"     => $v["p_id"],
                        "descriptions" => $v["desc"],
                        "source_240"   => $v["source_240"],
                        "duration"     => $v["duration"],
                        "status"       => 1,
                        "is_hide"      => 0,
                        "construct_id" => $pId,
                        "zpc_id"       => $v["id"],
                        "cover_thumb"  => !empty($v["cover_vertical"]) ? $v["cover_vertical"] : $v["cover_horizontal"],
                        "v_ext"        => $v["v_ext"],
                        "via"          => '91jgc',
                        'like_ct'      => rand(500, 5000),
                        'view_ct'      => rand(15000, 50000),
                        "created_at"   => TIMESTAMP,
                        "updated_at"   => TIMESTAMP,
                        "refresh_at"   => TIMESTAMP,
                    ]);

                    if ($videoId && $v["tags"]){
                        $tags = array_unique(array_filter(explode(",",$v["tags"])));
                        foreach ($tags as $tag){
                            $tagexist= TagsModel::where("name",$tag)->where("type",1)->first();
                            if ($tagexist){
                                $tagexist->child_count++;
                                $tagexist->save();
                                $tagid = $tagexist->id;
                            }else{
                                $tagid = TagsModel::insertGetId([
                                    "name"=>$tag,
                                    "type"=>1,
                                    "child_count"=>1,
                                ]);
                            }

                            $tagWhere  = ["tag_id" => $tagid, "video_id" => $videoId];
                            $tagDetail = VideosTagsModel::where($tagWhere)->first(['video_id']);
                            if(empty($tagDetail))
                            {
                                VideosTagsModel::insert(["tag_id"=>$tagid,"video_id"=>$videoId]);
                            }
                        }
                    }

                    VideosPlatesModel::insert(["video_id" => $videoId, "plate_id" => $pId]);
                    trigger_log("syncVideoHandle ... {$v['title']} === {$videoId} 视频新增成功");
                }


                $id = $data[count($data) - 1]['id'];
                trigger_log("syncVideoHandle 执行完成 id 为 ... {$id}");
            }


        } catch (Throwable $e) {
            trigger_log('出现异常'.$e->getMessage());
        }
    }

    public static function syncParentCate()
    {
        // 查询出一级分类
        $list = VideoElementValModel::where(['element_id' => 1])->orderByDesc('sort')->get();
        $list = to_array($list);

        foreach ($list as $v)
        {
            $detail = PlateModel::where(['vid' =>  $v['link_url']])->first(['id']);
            if(!empty($detail))
            {
                continue;
            }

            PlateModel::insert([
                'name'       => $v['name'],
                'vid'        => $v['link_url'],
                'sort'       => $v['sort'],
                'created_at' => strtotime($v['created_at']),
                'updated_at' => strtotime($v['updated_at']),
            ]);

        }

    }

    public static function syncCateTwo()
    {
        $list = PlateModel::where(['p_id' => 0])->select(['id', 'vid', 'status'])->get();
        $list = to_array($list);

        if(empty($list))
        {
            return false;
        }

        trigger_log(" two cate 1");
        foreach ($list as $v)
        {
            $element = VideoElementModel::where(['construct_id' => $v['vid'], 'content_type' => 4])->select(['id'])->get();
            $element = to_array($element);
            if(empty($element))
            {
                continue;
            }
            trigger_log(" two cate 2");

            $ids   = array_column($element, 'id');
            $eList = VideoElementValModel::whereIn('element_id', $ids)->orderByDesc('sort')->get();
            $eList = to_array($eList);
            foreach ($eList as $val)
            {

                $detail = PlateModel::where(['vid' =>  $val['link_url']])->first(['id']);
                if(!empty($detail))
                {
                    continue;
                }

                trigger_log(" two cate 3");
                PlateModel::insert([
                    'name'       => $val['name'],
                    'p_id'       => $v['id'],
                    'vid'        => $val['link_url'],
                    'sort'       => $val['sort'],
                    'created_at' => strtotime($val['created_at']),
                    'updated_at' => strtotime($val['updated_at']),
                ]);
            }
        }

        return true;
    }


    protected static function req($url, $params)
    {
//        trigger_log(" req 1...params...".var_export($params, true));
        $res = \tools\HttpCurl::syncPost($url, $params, ['Connection: Keep-Alive']);
//        trigger_log(" req 2...res...".var_export($res, true));
        return !empty($res) ? json_decode($res, true) : [];
    }

    const CK_LOCK_CONSTRUCT = 'ck:hj:sync:lock:construct';
    const RK_REMOTE_CONSTRUCT = 'rk:hj:remote:construct';
    const SYNC_CONSTRUCT_FIELDS = ['id', 'name', 'status', 'bind_type', 'bind_tags', 'type', 'api', 'params', 'click_num', 'created_at', 'updated_at'];

//    const REMOTE_ELEMENT_URL = 'http://zpc.we-cname.com/index.php/sync/element';
    const CK_LOCK_ELEMENT = 'ck:hj:sync:lock:element';
    const RK_REMOTE_ELEMENT = 'rk:hj:remote:element';
    const SYNC_ELEMENT_FIELDS = ['id', 'adm_title', 'title', 'sub_title', 'icon', 'construct_id', 'type', 'content_type', 'sub_content_type', 'sub_field', 'sub_maxnum', 'banner_scale', 'more_button', 'more_api', 'more_api_params', 'more_page_show_type', 'max_num', 'show_field', 'change_button', 'is_margin', 'status', 'sort', 'created_at', 'updated_at'];

    const CK_LOCK_ELEMENT_VALUE = 'ck:hj:sync:lock:element:value';
    const RK_REMOTE_ELEMENT_VALUE = 'rk:hj:remote:element:value';
    const SYNC_ELEMENT_VALUE_FIELDS = ['id', 'related_id', 'element_id', 'link_url', 'resource_url', 'redirect_type', 'router', 'name', 'desc', 'sort', 'open_type', 'created_at', 'updated_at'];

    const REMOTE_MV_URL = 'http://zpc.we-cname.com/index.php/sync/mv';
    const REMOTE_MV_ID_URL = 'http://zpc.we-cname.com/index.php/sync/mv_id';
    const CK_AT_MVS_LOCK = 'ck:hj:long:mv:at';
    const CK_LOCK_MV = 'ck:hj:sync:lock:long:mv';
    const RK_REMOTE_MV = 'rk:hj:remote:long:mv';
    const SYNC_MV_FIELDS = ['id', 'aff', '_id', 'p_id', 'title', 'mv_type', 'second_title', 'is_activity', 'is_recommend', 'source_origin', 'source_240', 'v_ext', 'duration', 'cover_vertical', 'cover_horizontal', 'directors', 'publisher', 'actors', 'category', 'tags', 'self_tag', 'tags_id', 'via', 'release_at', 'favorites', 'rating', 'count_play', 'count_play_fake', 'count_like', 'count_comment', 'count_reward', 'count_pay', 'income_coins', 'created_at', 'updated_at', 'refresh_at', 'callback_at', 'isfree', 'status', 'thumb_start_time', 'thumb_duration', 'is_hide', 'coins', 'music_id', 'enable_background', 'enable_soundtrack', 'is_delete', 'is_top', 'club_id', 'is_tester', 'desc', 'is_popular', 'is_tiptop', 'preview', 'mv_class', 'package_idstr', 'good_look', 'must_awesome', 'what_awesome', 'no_awesome', 'last_watch', 'topic_id', 'construct_id', 'editor_sort', 'play_ct'];

    const SIGN_KEY = '4a7bfc8d372c624f6330f5d45043690a';
    const MARK = 'HJ_';

    protected static function del_extra($data, $fields)
    {
        foreach ($data as $k => $v) {
            if (!in_array($k, $fields)) {
                unset($data[$k]);
            }
        }
        return $data;
    }



    protected static function change_data($fields, $record, $data): array
    {
        $rs = $record->toArray();
        $tmp_data = [];
        $change = false;
        foreach ($fields as $field) {
            if ($rs[$field] != $data[$field]) {
                $tmp_data[$field] = $data[$field];
                $change = true;
            }
        }
        return [$change, $tmp_data];
    }

    // ==============================================================================================



    // ==============================================================================================

    protected static function update_mv_record($record, $data)
    {
        $fields = ['_id', 'p_id', 'title', 'second_title', 'is_activity', 'is_recommend', 'source_origin', 'source_240', 'v_ext', 'duration', 'cover_vertical', 'cover_horizontal', 'directors', 'publisher', 'actors', 'category', 'tags', 'self_tag', 'tags_id', 'via', 'release_at', 'refresh_at', 'callback_at', 'isfree', 'status', 'thumb_start_time', 'thumb_duration', 'is_hide', 'coins', 'music_id', 'enable_background', 'enable_soundtrack', 'is_delete', 'is_top', 'club_id', 'is_tester', 'desc', 'is_popular', 'is_tiptop', 'preview', 'mv_class', 'package_idstr', 'good_look', 'must_awesome', 'what_awesome', 'no_awesome', 'topic_id', 'editor_sort', 'construct_id'];
        list($change, $tmp_data) = self::change_data($fields, $record, $data);
        if (!$change) {
            pf('无需更新', $record->id);
            return;
        }
        pf('更新数据' . $record->id, $tmp_data);
        $record->fill($tmp_data);
        $isOk = $record->save();
        test_assert($isOk, '无法更新结构数据');
    }

    protected static function defend_mv_del()
    {
        // 获取所有的ID进行判断
        $id = 0;
        while (true) {
            $params = [
                'app_name' => config('pay.app_name'),
                'time'     => time(),
                'node_id'  => $id,
                'limit'    => 10000,
            ];
            $params['sign'] = LibCrypt::make_sign($params, self::SIGN_KEY);
            $data = self::req(self::REMOTE_MV_ID_URL, $params);
            if (!count($data)) {
                break;
            }
            collect($data)->map(function ($item) {
                pf('添加远程ID', $item);
                redis()->sAdd(self::RK_REMOTE_MV, $item);
            });
            $id = $data[count($data) - 1];
        }

        LocalMvModel::select(['id', 'f_id'])
            ->where('f_id', 'like', self::MARK . '%')
            ->chunk(1000, function ($items) {
                collect($items)->map(function ($item) {
                    $f_id = str_replace(self::MARK, '', $item->f_id);
                    $exists = redis()->sismember(self::RK_REMOTE_MV, $f_id);
                    if ($exists) {
                        pf('已存在跳过', $item->f_id);
                        return;
                    }
                    $isOk = $item->delete();
                    test_assert($isOk, '删除视频记录异常');
                    pf('删除视频数据', '远程:' . $f_id . ' 远程标识' . $item->f_id . ' 本地:' . $item->id);
                });
            });
    }

    public static function mv()
    {
        $lock = redis()->get(self::CK_LOCK_MV);
        if ($lock) {
            return;
        }
        redis()->setnxttl(self::CK_LOCK_MV, 1, 1200);
        redis()->del(self::RK_REMOTE_MV);

        $at = redis()->get(self::CK_AT_MVS_LOCK);
        $at = $at ? $at : '';
        $last_at = $at;

        try {
            // 处理更新
            $id = 0;
            while (true) {
                $params = [
                    'app_name'  => config('pay.app_name'),
                    'time'      => time(),
                    'node_time' => $at,
                    'node_id'   => $id,
                    'limit'     => 100,
                ];
                $params['sign'] = LibCrypt::make_sign($params, self::SIGN_KEY);
                $data = self::req(self::REMOTE_MV_URL, $params);
                if (!count($data)) {
                    break;
                }
                collect($data)->map(function ($item) use (&$last_at) {
                    $item = self::del_extra($item, self::SYNC_MV_FIELDS);
                    $last_at = $item['updated_at'];

                    $record = LocalMvModel::where('f_id', self::MARK . $item['id'])->first();
                    if (!$record) {
                        $item['f_id'] = self::MARK . $item['id'];
                        $item['aff'] = 0;
                        $item['mv_type'] = LocalMvModel::MV_TYPE_LONG;
                        $item['count_like'] = 0;
                        $item['favorites'] = 0;
                        $item['count_comment'] = 0;
                        unset($item['id']);
                        unset($item['type']);
                        pf('新增数据', $item);
                        $isOk = LocalMvModel::create($item);
                        test_assert($isOk, '新增视频失败');
                        return;
                    }
                    self::update_mv_record($record, $item);
                });
                $id = $data[count($data) - 1]['id'];
            }

            // 处理删除
            self::defend_mv_del();

        } catch (Throwable $e) {
            pf('出现异常', $e->getMessage());
        }
        redis()->set(self::CK_AT_MVS_LOCK, $last_at);
        redis()->del(self::CK_LOCK_MV);
    }
}
