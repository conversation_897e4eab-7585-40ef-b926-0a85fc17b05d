<?php

/**
 * Class VideosController
 * @date 2023-09-29 12:35:11
 */
class VideosController extends BackendBaseController
{

    use \repositories\HoutaiRepository;
    /**
     * 列表数据过滤
     * @return Closure
     */
    protected function listAjaxIteration()
    {
        return function (VideosModel $item) {
            $relationList = $item->plates()->pluck('name', 'id')->toArray();
            $item->relationlist = array_keys($relationList);
            $item->relationName = implode(',', array_values($relationList));
            $tagsList = $item->tags()->pluck('name', 'id')->toArray();
            $item->tagsIds = array_keys($tagsList);
            $item->tagsStr = implode('#', array_values($tagsList));
            $item->hide_str = \VideosModel::SHOW_STATUS[$item->is_hide] ?? '未知';
            $item->refresh_at = date('Y-m-d H:i:s', $item->refresh_at);
            return $item;
        };
    }

    public function listAjaxAction(): bool
    {
        if (!$this->getRequest()->isXmlHttpRequest()) {
            return $this->ajaxError('加载错误');
        }

        // \DB::enableQueryLog();
        $pkName = $this->getPkName();
        /** @var \Illuminate\Database\Eloquent\Builder $modelBuilder */
        $modelBuilder = $this->getModelObject();
        $orderBy = $this->listAjaxOrder();
        if (empty($orderBy)) {
            $modelBuilder->orderBy($pkName, 'desc');
        } else {
            foreach ($orderBy as $column => $direction) {
                $modelBuilder->orderBy($column, $direction);
            }
        }

        $where = array_merge(
            $this->builderWhereArray(),
            $this->listAjaxWhere()
        );

        // web 类型视频列表加入条件
        array_push($where, ['video_type', '=', 1]);
        if (!empty($where)) {
            $params = $this->getRequest()->getQuery();
            $mid = $params['where']['plate_id'] == '__undefined__' ? '' : $params['where']['plate_id'];
            $key = array_search('plate_id', array_column($where, 0));
            if($key !== false) {
                unset($where[$key]);
            }
            if ($mid){
                $plated_ids = PlateModel::where("p_id",$mid)->pluck("id")->toArray();
                $video_ids = VideosPlatesModel::whereIn("plate_id",$plated_ids)->pluck("video_id")->toArray();
                $modelBuilder->where($where)->whereIn("id",$video_ids);
            }else{
                $modelBuilder->where($where);
            }
        }

        /** @var \Illuminate\Database\Eloquent\Model $modelBuilder */

        list($limit, $offset) = self::limitOffsetByGet();
        $oldBuilder = clone $modelBuilder;
        $data = $modelBuilder->limit($limit)->offset($offset)->get()->map($this->listAjaxIteration());

        $result = [
            'count' => empty($data) ? 0 : $data->count(),
            'data'  => $data,
            "msg"   => '',
            "desc"  => $this->getDesc($oldBuilder),
            'code'  => 0
        ];
        // trigger_logger(\DB::getQueryLog());
        return $this->ajaxReturn($result);
    }

    /**
     * 试图渲染
     * @return void
     */
    public function indexAction()
    {

        $plates = PlateModel::where("p_id",0)
            ->where("status",PlateModel::STATUS_PASS)
            ->get()->toArray();
        foreach ($plates as $key=> $row){
            $plates[$key]["child"] =  PlateModel::where("p_id",$row["id"])->pluck('name', 'id')->toArray();
        }
        $this->assign('plates', $plates);
        $this->assign('platesfirst', PlateModel::where("p_id",0)->where("status",PlateModel::STATUS_PASS)->pluck("name","id")->toArray());
        $tags = TagsModel::get()->where("type",1)->pluck('name', 'id')->toArray();
        $this->assign('tags', $tags);
        $this->display();
    }

    /**
     * 获取本控制器和哪个model绑定
     * @return string
     */
    protected function getModelClass(): string
    {
       return VideosModel::class;
    }

    /**
     * 定义数据操作的表主键名称
     * @return string
     */
    protected function getPkName(): string
    {
        return 'id';
    }

    /**
     * 定义数据操作日志
     * @return string
     */
    protected function getLogDesc(): string {
        return '';
    }

    protected function saveAfterCallback($model, $oldModel = NULL)
    {
        if ($model) {
            $this->savePlate($this->post, $model->id);
            $this->doSaveTags($this->post, $model->id);
        }
    }
    // 删除后删除关联的标签及模块
    public function deleteAfterCallback($model, $isDelete=1)
    {
        VideosPlatesModel::where("video_id",$model->id)->delete();
        VideosTagsModel::where("video_id",$model->id)->delete();
    }
    public function savePlate($data, $aid) {
        $insertData = [];
        if($data['plate_id']) {
            foreach($data['plate_id'] as $k => $v) {
                $insertData[$k]['plate_id'] = $v;
                $insertData[$k]['video_id'] = $aid;
                PlateModel::where('id', $v)->where('type', \ConstModel::TYPE_VIDEO)->increment('count_ct');
            }
            VideosPlatesModel::where('video_id', $aid)->delete();
            VideosPlatesModel::insert($insertData);
        }
    }

    public function doSaveTags($data, $id)
    {
        if(empty($data['tags'])) {
            return;
        }
        VideosTagsModel::where('video_id', $id)->delete();
        $tags = explode("#",$data['tags']);
        $insert = [];
        foreach ($tags as $k => $v) {
            $insert[$k]['video_id'] = $id;
            $tags_id = TagsModel::where("name",$v)->where("type",TagsModel::TYPE_VIDEO)->value("id");
            if (!$tags_id){
                $tags_id = TagsModel::insertGetId([
                    "name" => $v,
                    "type" => TagsModel::TYPE_VIDEO,
                    "child_count" => 1,
                ]);
            }
            $insert[$k]['tag_id'] = $tags_id;
            TagsModel::where('id', $tags_id)->increment('child_count');
        }
        VideosTagsModel::query()->insert($insert);
    }

    public function bat_set_plateAction()
    {
        if (!$this->getRequest()->isPost()) {
            return $this->ajaxError('请求错误');
        }
        $post = $this->postArray();
        $ids = explode(',', $post['ids'] ?? '');
        $ids = array_filter($ids);
        $plate_ids = $post['plate_id_arr'];
        try {
            transaction(function() use($ids, $plate_ids) {
                VideosPlatesModel::whereIn('video_id', $ids)->delete();
                foreach ($ids as $id) {
                    foreach ($plate_ids as $plate_id) {
                        VideosPlatesModel::create([
                            'video_id' => $id,
                            'plate_id' => $plate_id
                        ]);
                    }
                }
            });
            foreach ($plate_ids as $plate_id) {
                $videoids = VideosPlatesModel::where("plate_id",$plate_id)->pluck("video_id")->toArray();
                $count = VideosModel::whereIn("id",$videoids)->where("status",VideosModel::STATUS_PASS)->count();
                PlateModel::where("id",$plate_id)->update(["count_ct"=>$count]);
            }
            return $this->ajaxSuccessMsg('保存成功');
        }catch (Throwable $e) {
            return $this->ajaxError($e->getMessage());
        }
    }

    public function refreshTimeAction()
    {
        if (!$this->getRequest()->isPost()) {
            return $this->ajaxError('请求错误');
        }
        $post = $this->postArray();
        $ids = explode(',', $post['value'] ?? '');
        $ids = array_filter($ids);
        VideosModel::query()
            ->whereIn('id', $ids)
            ->update(['refresh_at' => TIMESTAMP]);
        return $this->ajaxSuccessMsg('刷新成功');
    }

    public function clearlistAction()
    {
        if (!$this->getRequest()->isPost()) {
            return $this->ajaxError('请求错误');
        }
        VideosModel::clearCasheNewList();
        return $this->ajaxSuccessMsg('刷新成功');
    }

    public function setBestAction()
    {
        if (!$this->getRequest()->isPost()) {
            return $this->ajaxError('请求错误');
        }
        $post = $this->postArray();
        $ids = explode(',', $post['value'] ?? '');
        $ids = array_filter($ids);
        VideosModel::query()
            ->whereIn('id', $ids)
            ->update(['is_best' => \VideosModel::BEST_YES]);
        return $this->ajaxSuccessMsg('操作成功');
    }

    /**
     * 删除数据
     * 后台全局公共方法
     */
    public function changehideAction(): bool
    {
        if (!$this->getRequest()->isPost()) {
            return $this->ajaxError('请求错误');
        }
        $post = $this->postArray();
        $ary = explode(',', $post['value'] ?? '');
        $type = $post['type']?:"show";

        try {
            \DB::beginTransaction();
            foreach ($ary as $id) {
                if (empty($id)) {
                    continue;
                }
                VideosModel::where("id",$id)->update([
                    "is_hide" => $type=="show" ? 0 : 1
                ]);
            }
            \DB::commit();
            return $this->ajaxSuccessMsg('操作成功');
        } catch (\Exception $e) {
            \DB::rollBack();
            return $this->ajaxError('操作错误');
        }
    }

}