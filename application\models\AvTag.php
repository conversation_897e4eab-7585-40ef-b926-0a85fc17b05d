<?php

/**
 * class AvTagModel
 *
 * @property int $id
 * @property string $name 标签名
 * @property int $video_num 视频数量
 * @property int $hot_num 热度值
 * @property int $is_index 是否首页显示
 * <AUTHOR>
 * @date 2020-02-26 12:59:00
 *
 * @mixin \Eloquent
 */

class AvTagModel extends BaseModel
{
    protected $table = "av_tag";

    protected $primaryKey = 'id';

    protected $fillable = ['id', 'name', 'title','video_num', 'is_index', 'sort', 'created_at', 'updated_at'];

    protected static $field = ['id', 'name', 'title', 'video_num', 'sort', 'is_index', 'created_at', 'updated_at'];

    const LIST_KEY = 'web:av:list:tags';

    public static function getTagList(): array
    {
        $tagArr = ["衣著", "身材", "交合", "玩法", "劇情", "角色", "地點", "雜項"];
//        $tagArr = ["衣著", "身材", "交合", "玩法", "劇情", "角色", "地點", "雜項", "未知"];
        $list   = self::allList();
        $rtn    = [];
        foreach ($list as $v)
        {
            $v['title'] = !empty($v['title']) ? $v['title'] : '未知';
            if(!array_key_exists($v['title'], $rtn))
            {
                $rtn[$v['title']]['title']   = $v['title'];
                $rtn[$v['title']]['list'][]  = $v;
            }else{
                array_push($rtn[$v['title']]['list'], $v);
            }
        }
//        echo "<pre>"; print_r($rtn);
        $result = [];
        foreach ($tagArr as $tag)
        {
            if(array_key_exists($tag, $rtn))
            {
                array_push($result, $rtn[$tag]);
            }
        }


        return $result;
    }

    public static function allList(): array
    {
        $key = $groupKey  = self::LIST_KEY;
        return cached($key)
            ->group($groupKey)
            ->chinese("av标签列表")
            ->fetchPhp(function () {
                $result = self::orderByDesc('sort')
                    ->get(self::$field);
                return !empty($result) ? $result->toArray() : [];
            }, 86400);
    }


    public static function clearCache()
    {
        cached(self::LIST_KEY)
            ->clearGroup();
    }

    const DETAIL_KEY = 'web:av:detail:tags:';

    public static function detail($id)
    {
        $key = self::DETAIL_KEY.$id;
        return cached($key)
            ->fetchPhp(function () use($id) {
                $result = self::where(['id' => $id])->first(self::$field);
                return !empty($result) ? $result->toArray() : [];
            });
    }
}