{{include file='../component/header.html'}}
<script type="text/javascript" src="/static/js/plugins/echarts/echarts.min.js" charset="utf-8"></script>
<script type="text/javascript" src="/static/js/highcharts.js"></script>
<style>
    .report-list{
        height: 120px;
    }
    .report-nav{
        height: 100%;
        width: 200px;
        border: 1px solid #D6D6D6;
        float: left;
        margin-right: 30px;
        margin-bottom: 10px;
    }
    .logo{
        background: url(/static/images/admin/nav-list.png) center no-repeat;
        background-size: 50px 50px;
        width: 20%;
    }
    .report-nav div{
        width: 50%;
        height: 100%;
        float: left;
    }
    .report-nav p{
        margin-top: 10px;
        line-height: 35px;
    }
    .report-nav p:last-child{
        font-weight: 900;
    }
</style>
<div class="report-list">
    <div class="report-nav">
        <div class="logo"></div>
        <div class="report-nav-item">
            <p>今日活跃</p>
            <p>{{$data.session}}</p>
        </div>
    </div>
    <div class="report-nav">
        <div class="logo"></div>
        <div class="report-nav-item">
            <p>今日注册</p>
            <p>{{$data.todayReg}}</p>
        </div>
    </div>
    <div class="report-nav" style="width: 280px;">
        <div class="logo" style="width: 20%"></div>
        <div class="report-nav-item" style="width: 80%">
            <p title="推广:邀请者为推广角色/用户:邀请者为普通用户/官网（无邀请人）：">用户来源(推广/用户/官网：)</p>
            <p>{{$data.ads_reg}}/{{$data.share_reg}}/{{$data.self_reg}}</p>
        </div>
    </div>
    <div class="report-nav" style="width: 280px;">
        <div class="logo" style="width: 20%"></div>
        <div style="width: 79%" class="report-nav-item">
            <p>今日视频通过数/总上传数</p>
            <p>{{$data.mv_accept}}/{{$data.mv_upload}}</p>
        </div>
    </div>
    <div class="report-nav">
        <div class="logo"></div>
        <div class="report-nav-item">
            <p>今日视频点击量</p>
            <p>{{$data.mv_watch}}</p>
        </div>
    </div>
    <div class="report-nav">
        <div class="logo"></div>
        <div class="report-nav-item">
            <p>今日视频评论数</p>
            <p>{{$data.mv_comment}}</p>
        </div>
    </div>
    <div class="report-nav">
        <div class="logo"></div>
        <div class="report-nav-item">
            <p>今日视频点赞数</p>
            <p>{{$data.mv_likes}}</p>
        </div>
    </div>
    <div class="report-nav">
        <div class="logo"></div>
        <div class="report-nav-item">
            <p>今日关注数</p>
            <p>{{$data.followed}}</p>
        </div>
    </div>
    <div class="report-nav" style="width: 280px;">
        <div class="logo" style="width: 20%"></div>
        <div class="report-nav-item" style="width: 79%" >
            <p>今日提现（总计/金币/代理）</p>
            <p>{{$data.draw}}/{{$data.draw_gold}}/{{$data.draw_proxy}}</p>
        </div>
    </div>
    <div class="report-nav" style="width: 500px;">
        <div class="logo" style="width: 20%"></div>
        <div class="report-nav-item" style="width: 79%">
            <p>今日充值(总计/vip/金币)</p>
            <p>({{$data.pay}}/{{$data.pay_vip}}/{{$data.pay_gold}})</p>
        </div>
    </div>
    <div class="report-nav">
        <div class="logo"></div>
        <div class="report-nav-item">
            <p>今日话题浏览数</p>
            <p>{{$data.news_view}}</p>
        </div>
    </div>
</div>
<br/>
<div class="row">
    <div id="userRegLog" style="width: 100%; height:400px;"></div>
</div>
<script type="text/javascript">
    var date = {{$data.reg['date']}};
    var data = {{$data.reg['reg_num_ip']}};

    $(document).ready(function() {
        // 表示图为柱形图
        var chart = {
            type: 'column'
        };
        // 主标题
        var title = {
            text: '日注册用户ip去重'
        };
        // 子标题
        var subtitle = {
            text: ''
        };
        // X 轴
        var xAxis = {
            categories: date,
            crosshair: true
        };
        // Y 轴
        var yAxis = {
            min: 0,
            title: {
                text: '注册量'
            }
        };
        // 提示
        var tooltip = {
            headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
            pointFormat: '<td style="padding:0"><b>{point.y}人</b></td></tr>',
            footerFormat: '</table>',
            shared: true,
            useHTML: true
        };
        var plotOptions = {
            column: {
                pointPadding: 0.2,
                borderWidth: 0,
                dataLabels: {
                    enabled: true //设置显示对应y的值
                }
            }
        };
        var credits = {
            enabled: false
        };
        // 数据
        var series= [{
            name: '最近7天用户注册',
            data: data
        }];

        var json = {};
        json.chart = chart;
        json.title = title;
        json.subtitle = subtitle;
        json.tooltip = tooltip;
        json.xAxis = xAxis;
        json.yAxis = yAxis;
        json.series = series;
        json.plotOptions = plotOptions;
        json.credits = credits;
        $('#userRegLog').highcharts(json);

    });
</script>
</html>