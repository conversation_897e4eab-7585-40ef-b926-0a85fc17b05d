<?php


namespace service;

use CommentsLikeModel;
use VideosCommentsLikeModel;
use VideosCommentsModel;
use CommentsModel;
use MemberModel;

class contentsService
{
    public function list_comment(MemberModel $member,$via, $cid, $page, $limit = 30){
        // via 1-吃瓜 2-视频
        if ($via == 1){
            CommentsModel::setWatchUser($member);
            if ($page == 1){
                $list = CommentsModel::list_first($cid,$via, 1, 30);
            }else{
                $coids = CommentsModel::list_first_ids($cid,$via);
                if (count($coids) < $limit){
                    return [];
                }
                $list = CommentsModel::list_comments($cid,$via, $coids, $page, $limit);
            }

            return collect($list)->map(function ($item){
                $item->items = CommentsModel::fir_sec_comment($item->cid, $item->coid);
                return $item;
            });
        }else{
            VideosCommentsModel::setWatchUser($member);
            if ($page == 1){
                $list = VideosCommentsModel::list_first($cid,$via, 1, 30);
            }else{
                $coids = VideosCommentsModel::list_first_ids($cid,$via);
                if (count($coids) < $limit){
                    return [];
                }
                $list = VideosCommentsModel::list_comments($cid,$via, $coids, $page, $limit);
            }
            return collect($list)->map(function ($item){
                $item->items = VideosCommentsModel::fir_sec_comment($item->cid, $item->coid);
                return $item;
            });
        }

    }

    public function list_replys(MemberModel $member, $cid, $coid, $page, $limit,$via=1){
        if ($via == 1){
            CommentsModel::setWatchUser($member);
            return CommentsModel::list_replys($cid, $coid, $page, $limit);
        }else{
            VideosCommentsModel::setWatchUser($member);
            return VideosCommentsModel::list_replys($cid, $coid, $page, $limit);
        }

    }

    public function like_comment(MemberModel $member, $coid,$via=1){
        if ($via == 1){
            $comment = CommentsModel::find($coid);
            test_assert($comment, '评论不存在');
            transaction(function () use ($member, $coid){
                /** @var CommentsLikeModel $like */
                $like = CommentsLikeModel::query()
                    ->where('coid', $coid)
                    ->where('aff', $member->aff)->first();
                if (empty($like)) {
                    $like = CommentsLikeModel::create([
                        'aff'        => $member->aff,
                        'coid'        => $coid,
                    ]);
                    test_assert($like , '添加点赞数据失败');
                    jobs([CommentsModel::class, 'incrementLikeNum'], [$coid]);
                    redis()->sAdd(sprintf(CommentsLikeModel::CONTENTS_COMMENTS_LIKE, $member->aff), $coid);
                } else {
                    test_assert($like->delete() , '清理点赞数据失败');
                    jobs([CommentsModel::class, 'decrementLikeNum'], [$coid]);
                    redis()->sRem(sprintf(CommentsLikeModel::CONTENTS_COMMENTS_LIKE, $member->aff), $coid);
                }
            });
        }else{
            $comment = VideosCommentsModel::find($coid);
            test_assert($comment, '评论不存在');
            transaction(function () use ($member, $coid, $comment){
                /** @var CommentsLikeModel $like */
                $like = VideosCommentsLikeModel::query()
                    ->where('coid', $coid)
                    ->where('aff', $member->aff)->first();
                if (empty($like)) {
                    $like = VideosCommentsLikeModel::create([
                        'aff'        => $member->aff,
                        'coid'        => $coid,
                    ]);
                    test_assert($like , '添加点赞数据失败');
                    jobs([VideosCommentsModel::class, 'incrementLikeNum'], [$coid]);
                    redis()->sAdd(sprintf(VideosCommentsLikeModel::CONTENTS_COMMENTS_LIKE, $member->aff), $coid);
                } else {
                    test_assert($like->delete() , '清理点赞数据失败');
                    jobs([VideosCommentsModel::class, 'decrementLikeNum'], [$coid]);
                    redis()->sRem(sprintf(VideosCommentsLikeModel::CONTENTS_COMMENTS_LIKE, $member->aff), $coid);
                }

                $key = sprintf(VideosCommentsModel::GP_CON_COM_LIST,$comment->cid);
                cached('')->clearGroup($key);
            });
        }

    }
}