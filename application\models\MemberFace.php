<?php
use Carbon\Carbon;
/**
 * class AreaModel
 *
 * @property int $id
 * @property string $name 类型名称
 * @property int $status 状态  1启用 2未启用
 * @property int $sort 排序 越大越前
 * @property int $created_at 创建时间
 * @property int $updated_at 修改时间
 *
 * @date 2020-01-08 17:09:02
 *
 * @mixin \Eloquent
 */
class MemberFaceModel extends BaseModel
{
    protected $table = "member_face";
    protected $guarded = 'id';
    public $timestamps = false;
    protected $fillable = [
        'aff',
        'material_id',
        'ground',
        'ground_w',
        'ground_h',
        'thumb',
        'thumb_w',
        'thumb_h',
        'face_thumb',
        'face_thumb_w',
        'face_thumb_h',
        'is_delete',
        'status',
        'reason',
        'created_at',
        'updated_at',
        'type',
        'coins',
    ];

    const STATUS_WAIT = 0;
    const STATUS_DOING = 1;
    const STATUS_SUCCESS = 2;
    const STATUS_FAIL = 3;
    const STATUS_TIPS = [
        self::STATUS_WAIT    => '排队中',
        self::STATUS_DOING   => '处理中',
        self::STATUS_SUCCESS => '已成功',
        self::STATUS_FAIL    => '已失败',
    ];
    const DELETE_NO = 0;
    const DELETE_OK = 1;
    const DELETE_TIPS = [
        self::DELETE_NO => '未删除',
        self::DELETE_OK => '已删除',
    ];

    const TYPE_COINS = 0;
    const TYPE_TIME = 1;
    const TYPE_TIPS = [
        self::TYPE_COINS => '金币',
        self::TYPE_TIME => '免费次数',
    ];
    const REDIS_KEY = 'facecate';
    const REDIS_KEY_GROUP = 'gpfacecate';
    const SE_MY_FACE = ['id', 'ground', 'ground_w', 'ground_h', 'thumb', 'thumb_w', 'thumb_h', 'face_thumb', 'face_thumb_w', 'face_thumb_h', 'status', 'reason', 'created_at'];
    protected $appends = ['status_str'];
    public function getStatusStrAttribute()
    {
        return isset($this->attributes["status"]) ? self::STATUS_TIPS[$this->attributes["status"]] : "未知状态";
    }
    public function setGroundAttribute($value)
    {
        parent::resetSetPathAttribute('ground', $value);
    }

    public function getGroundAttribute(): string
    {
        return isset($this->attributes['ground']) ? url_image($this->attributes['ground']) : '';
    }

    public function setThumbAttribute($value)
    {
        parent::resetSetPathAttribute('thumb', $value);
    }

    public function getThumbAttribute(): string
    {
        return isset($this->attributes['thumb']) ? url_image($this->attributes['thumb']) : '';
    }

    public function setFaceThumbAttribute($value)
    {
        parent::resetSetPathAttribute('face_thumb', $value);
    }

    public function getFaceThumbAttribute(): string
    {
        return isset($this->attributes['face_thumb']) ? url_image($this->attributes['face_thumb']) : '';
    }

    public static function create_record($aff, $material_id, $type, $coins, $ground, $ground_w, $ground_h, $thumb, $thumb_w, $thumb_h)
    {
        $data = [
            'aff'          => $aff,
            'material_id'  => $material_id,
            'ground'       => $ground,
            'ground_w'     => $ground_w,
            'ground_h'     => $ground_h,
            'thumb'        => $thumb,
            'thumb_w'      => $thumb_w,
            'thumb_h'      => $thumb_h,
            'face_thumb'   => '',
            'face_thumb_w' => 0,
            'face_thumb_h' => 0,
            'is_delete'    => self::DELETE_NO,
            'status'       => self::STATUS_WAIT,
            'reason'       => '',
            'created_at'   => Carbon::now(),
            'updated_at'   => Carbon::now(),
            'type'         => $type,
            'coins'        => $coins,
        ];
        return self::create($data);
    }

    public static function create_customize_record($aff, $type, $coins, $ground, $ground_w, $ground_h, $thumb, $thumb_w, $thumb_h)
    {
        $data = [
            'aff'          => $aff,
            'material_id'  => 0,
            'ground'       => $ground,
            'ground_w'     => $ground_w,
            'ground_h'     => $ground_h,
            'thumb'        => $thumb,
            'thumb_w'      => $thumb_w,
            'thumb_h'      => $thumb_h,
            'face_thumb'   => '',
            'face_thumb_w' => 0,
            'face_thumb_h' => 0,
            'is_delete'    => self::DELETE_NO,
            'status'       => self::STATUS_WAIT,
            'reason'       => '',
            'created_at'   => date('Y-m-d H:i:s'),
            'updated_at'   => date('Y-m-d H:i:s'),
            'type'         => $type,
            'coins'        => $coins,
        ];
        return self::create($data);
    }

    public static function list_my_face($aff, $status, $page, $limit)
    {
        if ($status == 0 || $status == 1 ){
            $status_array = [0,1];
        }else{
            $status_array = [$status];
        }
        return self::select(self::SE_MY_FACE)
            ->where('aff', $aff)
            ->whereIn('status', $status_array)
            ->where('is_delete', self::DELETE_NO)
            ->forPage($page, $limit)
            ->get();
    }
}
