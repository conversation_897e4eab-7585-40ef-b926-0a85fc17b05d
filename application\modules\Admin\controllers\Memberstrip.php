<?php

use service\AiSdkService;
/**
 * Class MemberstripController
 *
 * @date 2024-01-02 16:01:32
 */
class MemberstripController extends BackendBaseController
{
    use \repositories\HoutaiRepository;
    /**
     * 列表数据过滤
     * @return Closure
     *
     * @date 2019-12-02 17:08:03
     */
    protected function listAjaxIteration()
    {
        return function (MemberStripModel $item) {
            $item->status_str = MemberStripModel::STATUS_TIPS[$item->status];
            $item->pay_type_str = MemberStripModel::PAY_TYPE_TIPS[$item->pay_type];
            return $item;
        };
    }

    /**
     * 试图渲染
     * @return string
     *
     * @date 2024-01-02 16:01:32
     */
    public function indexAction()
    {
        $this->display();
    }


    /**
     * 获取对应的model名称
     * @return string
     *
     * @date 2024-01-02 16:01:32
     */
    protected function getModelClass(): string
    {
       return MemberStripModel::class;
    }

    /**
     * 定义数据操作的表主键名称
     * @return string
     *
     * @date 2024-01-02 16:01:32
     */
    protected function getPkName(): string
    {
        return 'id';
    }

    /**
     * 定义数据操作日志
     * @return string
     *
     * @date 2019-11-04 17:19:41
     */
    protected function getLogDesc(): string {
        // TODO: Implement getLogDesc() method.
        return '';
    }

    public function retryAction()
    {
        try {
            $post = $this->postArray();
            $ary = explode(',', $post['_pk'] ?? '');
            $ary = array_filter($ary);
            MemberStripModel::whereIn('id', $ary)
                ->whereIn('status', [MemberStripModel::STATUS_FAIL, MemberStripModel::STATUS_WAIT, MemberStripModel::STATUS_DOING])
                ->get()
                ->map(function ($item) {
                    $item->status = MemberStripModel::STATUS_WAIT;
                    $item->reason = '';
                    $isOk = $item->save();
                    test_assert($isOk, '系统异常');
                    $id = $item->id;
                    bg_run(function () use ($id){
                        errLog("开始处理id:".$id);
                        AiSdkService::strip($id);
                    });
                });
            return $this->ajaxSuccessMsg('操作成功');
        } catch (Exception $e) {
            return $this->ajaxError($e->getMessage());
        }
    }

}