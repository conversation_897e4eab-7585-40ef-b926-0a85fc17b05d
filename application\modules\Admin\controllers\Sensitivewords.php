<?php

/**
 * Class SensitivewordsController
 *
 * @date 2023-08-03 04:19:32
 */
class SensitivewordsController extends BackendBaseController
{

    use \repositories\HoutaiRepository;

    /**
     * 列表数据过滤
     *
     * @return Closure
     */
    protected function listAjaxIteration()
    {
        return function (SensitiveWordsModel $item) {
            $item->setHidden([]);
            $item->status_str = SensitiveWordsModel::STATUS_TIPS[$item->status];
            return $item;
        };
    }

    public function importAction(){
        $text = trim($_POST['text'] ?? '');
        $ary = collect(explode("\n", $text))->unique()->map(function ($v) {
            $v = trim($v);
            if (empty($v)) {
                return null;
            }

            return ['word' => $v, 'status' => SensitiveWordsModel::STATUS_YES];
        })->filter()->values()->chunk(500);
        $ary->map(function ($items) {
            SensitiveWordsModel::insert($items);
        });
        return $this->ajaxSuccessMsg('ok');
    }

    public function clearCacheAction(){
        cached('sensitive_words')->clearCached();
        return $this->ajaxSuccessMsg('ok');
    }

    /**
     * 试图渲染
     *
     * @return void
     */
    public function indexAction()
    {
        $this->display();
    }

    /**
     * 获取本控制器和哪个model绑定
     *
     * @return string
     */
    protected function getModelClass(): string
    {
        return SensitiveWordsModel::class;
    }

    /**
     * 定义数据操作的表主键名称
     *
     * @return string
     */
    protected function getPkName(): string
    {
        return 'id';
    }

    /**
     * 定义数据操作日志
     *
     * @return string
     */
    protected function getLogDesc(): string
    {
        return '';
    }
}