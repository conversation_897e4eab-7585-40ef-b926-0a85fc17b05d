<?php


use Illuminate\Database\Eloquent\Model;

/**
 * class RelationshipsModel
 *
 * @property int $cid 文章id
 * @property int $mid metas表的的id
 *
 * @property MetasModel $meta
 *
 * @mixin \Eloquent
 */
class RelationshipsModel extends BaseModel
{

    protected $table = "relationships";

    protected $primaryKey = 'cid';

    public $incrementing = false;

    protected $fillable = ['mid', 'cid'];

    protected $guarded = 'cid';

    public $timestamps = false;

    public function content(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(ContentsModel::class, 'cid', 'cid');
    }

    public function meta(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(MetasModel::class, 'mid', 'mid');
    }


}
