<?php
use Carbon\Carbon;
/**
 * class AdminLogModel
 *
 * @property int $id
 * @property string $username 账号
 * @property string $action 操作
 * @property string $ip 操作ip
 * @property string $log 操作详情
 * @property string $referrer 操作url来源
 * @property string $context 操作http上下文,含有cookie,post,get
 * @property string $created_at 操作时间
 *
 * <AUTHOR>
 * @date 2020-01-17 16:08:56
 *
 * @mixin \Eloquent
 */
class AdminLogModel extends \Illuminate\Database\Eloquent\Model
{
    protected $table = "admin_log";

    protected $primaryKey = 'id';

    protected $fillable = ['username', 'action', 'ip', 'log', 'referrer', 'context', 'created_at'];

    protected $guarded = 'id';


    const UPDATED_AT = null;

    const ACTION_CREATED = 'created';
    const ACTION_UPDATED = 'updated';
    const ACTION_DELETED = 'deleted';

    const ACTION_TIPS = [
        self::ACTION_CREATED => "创建",
        self::ACTION_UPDATED => "更新",
        self::ACTION_DELETED => "删除",
    ];

    public function getCreatedAtAttribute($value): string
    {
        if ($value == 0) {
            $value = null;
        }
        $date = Carbon::parse($value);
        $date->timezone = 'Asia/Shanghai';
        return $date->format('Y-m-d H:i:s');
    }
}
