<?php


namespace tools;


class mp4Upload
{
    public static function accept(array $data, $action = 'onLineMv')
    {
        $data['sign'] = CommonService::sign($data);
        if (in_array(ini_get('yaf.environ'), ['develop', 'test'])) {
            $data['notifyUrl'] = 'https://cg2-api.hyys.info/index.php/notify/' . $action;
        } else {
            $data['notifyUrl'] = 'https://bpi1.51sp1.com/index.php/notify/' . $action;
        }
        $result = HttpCurl::post(config('mp4.accept'), $data);
        if ($result != 'success') {
            trigger_error('审核失败-----' . print_r($result, true));
            return false;
        }
        return true;
    }

    public static function destroy(array $data)
    {
        $result = HttpCurl::post(config('mp4.destroy'), $data);
        if ($result == 'success') {
            return false;
        }
        return true;
    }
}