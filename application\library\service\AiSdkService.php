<?php

namespace service;

use CURLFile;
use LibCrypt;
use LibUpload;
use Throwable;
use MemberStripModel;
use MemberVideoFaceModel;
use MemberFaceModel;
use VideoSourceModel;

class AiSdkService
{
    const STRIP_API = 'http://ai.ycomesc.live/api/img/getImg';
    const STRIP_BACK_API = 'http://91porna-www-a.we-cname3.com/index.php/notify/ai_ty';
    const STRIP_LOG_FILE = '/storage/logs/strip.log';

    const IMAGE_FACE_API = 'https://ai-1.yesebo.net/head/getImg';
    const IMAGE_FACE_BACK_API = 'http://91porna-www-a.we-cname3.com/index.php/notify/change_face';
    const IMAGE_FACE_LOG_FILE = '/storage/logs/img_face.log';

    public static function js($data)
    {
        return json_encode($data, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES | JSON_PRETTY_PRINT);
    }

    public static function wr_strip_log($tip, $data)
    {
        wf($tip, $data, false, self::STRIP_LOG_FILE);
    }

    public static function wr_image_face_log($tip, $data)
    {
        wf($tip, $data, false, self::IMAGE_FACE_LOG_FILE);
    }

    /**
     * @throws \Exception
     */
    protected static function call_ai_strip_api($id, $fr)
    {
        $pathinfo = pathinfo($fr);
        $imagename = $pathinfo["basename"];
        errLog("开始处理fr:".$fr);
        $image = file_get_contents($fr);
        test_assert($image, '请求远程异常:' . $fr);
        $from = APP_PATH . '/storage/data/images/' .$imagename;
        $dirname = dirname($from);
        if (!is_dir($dirname) || !file_exists($dirname)) {
            mkdir($dirname, 0755, true);
        }
        errLog("开始执行任务1");
        self::wr_strip_log('写入文件', $from);
        $rs = file_put_contents($from, $image);
        test_assert($rs, '无法写入文件:' . $from);
        $cover = new CURLFile(realpath($from), mime_content_type($from));
        $data = [
            'image'    => $cover,
            'id'       => $id,
            'callback' => self::STRIP_BACK_API,
            'project'  => config('pay.app_name')
        ];
        errLog("开始执行任务2".print_r($data,true));
        errLog("开始执行任务2");
        self::wr_strip_log('请求参数', $data);
        $rs = LibUpload::execCurl(self::STRIP_API, $data);
        errLog("开始执行任务3");
        $code = (int)($rs['code'] ?? 0);
        test_assert($code == 200, '请求远程AI绘图异常');
        errLog("开始执行任务code$code");
        unlink($from);
    }

    /**
     * @throws \Exception
     */
    protected static function upload_img($fr, $type = 0)
    {
        $type == 0 ? self::wr_strip_log('开始处理', $fr) : self::wr_image_face_log('开始处理', $fr);
        $image = file_get_contents($fr);
        test_assert($image, '请求远程异常' . $fr);
        $md5 = substr(md5($fr), 0, 16);
        $to = APP_PATH . '/storage/data/images/' . $md5 . '_to';
        $dirname = dirname($to);
        if (!is_dir($dirname) || !file_exists($dirname)) {
            mkdir($dirname, 0755, true);
        }
        $rs = file_put_contents($to, $image);
        test_assert($rs, '无法写入文件:' . $to);

        $flag = false;
        for ($i = 1; $i <= 3; $i++) {
            $return = LibUpload::upload2Remote(uniqid(), $to, 'upload');
            $type == 0 ? self::wr_strip_log('上传返回', $return) : self::wr_image_face_log('上传返回', $return);
            if ($return && $return['code'] == 1) {
                $flag = true;
                break;
            }
        }
        test_assert($flag, '上传图片异常');
        unlink($to);
        $type == 0 ? self::wr_strip_log('处理完成', $return['msg']) : self::wr_image_face_log('处理完成', $return['msg']);
        return $return['msg'];
    }

    /**
     * @throws \Exception
     */
    public static function strip_back(): bool
    {
        try {
            $id = (int)($_POST['id'] ?? 0);
            $img = trim($_POST['image'] ?? '');
            $code = (int)($_POST['code'] ?? 0);
            self::wr_strip_log('收到回调', $_POST);
            test_assert($id, '回调异常');

            /** @var MemberStripModel $item */
            $item = MemberStripModel::where('id', $id)
                ->where('status', MemberStripModel::STATUS_DOING)
                ->first();
            if (!$item) {
                exit('success');
            }

            if ($code == 0) {
                $item->status = MemberStripModel::STATUS_FAIL;
                $item->reason = 'AI脱衣失败';
                $isOk = $item->save();
                test_assert($isOk, '系统异常');
                exit('success');
            }

            // 上传远程图片
            test_assert($img, '回调成功,图片地址异常');
            list($w, $h) = getimagesize($img);
            $url = self::upload_img($img);
            $item->status = MemberStripModel::STATUS_SUCCESS;
            $item->strip_thumb = $url;
            $item->strip_thumb_w = $w;
            $item->strip_thumb_h = $h;
            $isOk = $item->save();
            test_assert($isOk, '系统异常');
            exit('success');
        } catch (Throwable $e) {
            self::wr_strip_log('出现异常', $e->getMessage());
            exit('fail');
        }
    }

    public static function strip($task_id)
    {
        try {
            $item = MemberStripModel::where('id', $task_id)
                ->where('status', MemberStripModel::STATUS_WAIT)
                ->first();
            test_assert($item, '任务不存在');
            self::call_ai_strip_api($item->id, TB_IMG_ADM_US . parse_url($item->thumb, PHP_URL_PATH));
            $item->status = MemberStripModel::STATUS_DOING;
            $isOk = $item->save();
            test_assert($isOk, '系统异常');
        } catch (Throwable $e) {
            self::wr_strip_log('出现异常', $e->getMessage());
        }
    }

    protected static function gen_filename(): string
    {
        while (true) {
            $rand = '' . mt_rand(1000, 9999) . '-' . time() . '-' . mt_rand(1000, 9999);
            $file = substr(md5($rand), 0, 16) . '.mp4';
            $tmp_file = APP_PATH . '/storage/material/' . $file;
            if (!file_exists($tmp_file)) {
                return $file;
            }
        }
    }



    public static function image_face_api($id, $fr, $fr2)
    {
        $pathinfo = pathinfo($fr);
        $imagename = $pathinfo["basename"];
        $pathinfo = pathinfo($fr2);
        $imagename2 = $pathinfo["basename"];
        self::wr_image_face_log('开始处理', $fr);
        $image = file_get_contents($fr);
        test_assert($image, '请求远程异常:' . $fr);
        $from = APP_PATH . '/storage/data/images/' . $imagename;
        $dirname = dirname($from);
        if (!is_dir($dirname) || !file_exists($dirname)) {
            mkdir($dirname, 0755, true);
        }
        self::wr_image_face_log('写入文件', $from);
        $rs = file_put_contents($from, $image);
        test_assert($rs, '无法写入文件:' . $from);

        self::wr_image_face_log('开始处理', $fr2);
        $image = file_get_contents($fr2);
        test_assert($image, '请求远程异常:' . $fr2);
        $from2 = APP_PATH . '/storage/data/images/' . $imagename2;
        $dirname = dirname($from2);
        if (!is_dir($dirname) || !file_exists($dirname)) {
            mkdir($dirname, 0755, true);
        }
        self::wr_image_face_log('写入文件', $from2);
        $rs = file_put_contents($from2, $image);
        test_assert($rs, '无法写入文件:' . $from2);

        $cover = new CURLFile(realpath($from), mime_content_type($from));
        $cover2 = new CURLFile(realpath($from2), mime_content_type($from2));
        $data = [
            'source'   => $cover2,
            'target'   => $cover,
            'id'       => $id,
            'callback' => self::IMAGE_FACE_BACK_API,
            'project'  => config('pay.app_name')
        ];
        self::wr_image_face_log('请求参数', $data);
        $rs = LibUpload::execCurl(self::IMAGE_FACE_API, $data);
        $url = $rs['imageUrl'] ?? '';
        self::wr_image_face_log('返回响应:', $rs);
        test_assert($url, '请求远程AI换头异常');
        unlink($from);
    }

    public static function image_face_back()
    {
        try {
            $id = (int)($_POST['id'] ?? 0);
            $img = trim($_POST['image'] ?? '');
            $code = (int)($_POST['code'] ?? 0);

            self::wr_image_face_log('收到换脸回调', $_POST);
            test_assert($id, '回调异常');

            $item = MemberFaceModel::where('id', $id)
                ->where('status', MemberFaceModel::STATUS_DOING)
                ->first();
            if (!$item) {
                exit('success');
            }

            if ($code == 0) {
                $item->status = MemberFaceModel::STATUS_FAIL;
                $item->reason = '换脸失败';
                $isOk = $item->save();
                test_assert($isOk, '系统异常');
                exit('success');
            }

            // 上传远程图片
            test_assert($img, '回调成功,图片地址异常');
            list($w, $h) = getimagesize($img);
            $url = self::upload_img($img, 1);
            $item->status = MemberFaceModel::STATUS_SUCCESS;
            $item->face_thumb = $url;
            $item->face_thumb_w = $w;
            $item->face_thumb_h = $h;
            $item->updated_at = \Carbon\Carbon::now();
            $isOk = $item->save();
            test_assert($isOk, '系统异常');
            exit('success');
        } catch (Throwable $e) {
            self::wr_image_face_log('出现异常', $e->getMessage());
            exit('fail');
        }
    }

    public static function image_face($task_id)
    {
        try {
            $item = MemberFaceModel::useWritePdo()
                ->where('id', $task_id)
                ->where('status', MemberFaceModel::STATUS_WAIT)
                ->first();
            test_assert($item, '任务不存在');
            self::image_face_api($item->id, TB_IMG_ADM_US . parse_url($item->ground, PHP_URL_PATH), TB_IMG_ADM_US . parse_url($item->thumb, PHP_URL_PATH));
            $item->status = MemberFaceModel::STATUS_DOING;
            $isOk = $item->save();
            test_assert($isOk, '系统异常');
        } catch (Throwable $e) {
            self::wr_image_face_log('出现异常', $e->getMessage());
        }
    }
}