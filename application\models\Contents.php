<?php

use Illuminate\Database\Eloquent\Model;

/**
 * class ContentsModel
 *
 *
 * @property int $cid
 * @property string $title
 * @property string $slug
 * @property \Carbon\Carbon $created
 * @property int $modified
 * @property string $text
 * @property int $order
 * @property int $authorId
 * @property string $template
 * @property string $type
 * @property string $status
 * @property string $password
 * @property int $commentsNum
 * @property string $allowComment 允许评论
 * @property string $allowPing 允许被引用
 * @property string $allowFeed 允许在聚合中出现
 * @property int $parent
 * @property int $is_home 是否在首页展示
 * @property int $home_top
 * @property int $is_slice 默认处于切片状态
 * @property int $app_hide app端隐藏
 * @property int $favorite_num 收藏数
 * @property int $web_show
 * @property int $view 浏览量
 * @property int $fake_view 显示浏览量
 * @property int $like_num 点赞数
 *
 * @property array<FieldsModel>|\Illuminate\Database\Eloquent\Collection $fields
 * @property ?UsersModel $author
 * @property array<RelationshipsModel>|\Illuminate\Database\Eloquent\Collection $relationships
 *
 * @mixin \Eloquent
 */
class ContentsModel extends BaseModel
{
    protected $table = 'contents';
    protected $primaryKey = 'cid';
    protected $fillable
        = [
            'cid',
            'title',
            'slug',
            'created',
            'modified',
            'text',
            'order',
            'authorId',
            'template',
            'type',
            'status',
            'password',
            'commentsNum',
            'allowComment',
            'allowPing',
            'allowFeed',
            'parent',
            'is_home',
            'home_top',
            'is_slice',
            'app_hide',
            'favorite_num',
            'web_show',
            'view',
            'fake_view',
            'like_num',
            'tid',
            'cate_ids',
            'tag_ids',
            'cover',
        ];
    protected $guarded = 'cid';
    public $timestamps = true;
    protected $dateFormat = 'U';
    const UPDATED_AT = 'modified';
    const CREATED_AT = 'created';
    protected $casts
        = [
            'created'  => 'datetime:Y-m-d H:i:s',
            'modified' => 'datetime:Y-m-d H:i:s',
        ];
    const TYPE_POST = 'post';
    const TYPE_ATTACHMENT = 'attachment';
    const TYPE_PAGE = 'page';
    const TYPE_SKITS = 'skits';
    const TYPE_BIG_WENT = 'big_went';
    const TYPE
        = [
            self::TYPE_POST       => '文章',
            self::TYPE_ATTACHMENT => '附件',
            self::TYPE_PAGE       => '单页',
            self::TYPE_SKITS      => '短剧',
            self::TYPE_BIG_WENT   => '大事件',
        ];
    const STATUS_PUBLISH = 'publish';
    const STATUS_PRIVATE = 'private';
    const STATUS_WAITING = 'waiting';
    const STATUS_HIDDEN = 'hidden';
    const STATUS_PASSWORD = 'password';
    const STATUS_SECRET = 'secret';
    const STATUS
        = [
            self::STATUS_PUBLISH  => '公开',
            self::STATUS_PRIVATE  => '私密',
            self::STATUS_WAITING  => '待审核',
            self::STATUS_HIDDEN   => '隐藏',
            self::STATUS_PASSWORD => '密码保护',
            self::STATUS_SECRET   => '秘闻',
        ];

    const APP_HIDE_NO = 0;
    const APP_HIDE_YES = 1;
    const APP_HIDE
        = [
            self::APP_HIDE_NO  => 'APP显示',
            self::APP_HIDE_YES  => 'APP隐藏',
        ];

    const WEB_SHOW_NO = 0;
    const WEB_SHOW_YES = 1;
    const WEB_SHOW
        = [
            self::APP_HIDE_NO  => 'web显示',
            self::APP_HIDE_YES  => 'web隐藏',
        ];

    const CONTENTS_RANK_VIEW = 'contents:rank:view:v2:%s';
    const FAKE_VIEW_MULTIPLE = 9.1;

    public static function incrementFavoriteNum($id, $num = 1)
    {
        return self::where('cid', $id)->increment('favorite_num', $num);
    }

    public static function incrementLikeNum($id, $num = 1)
    {
        return self::where('cid', $id)->increment('like_num', $num);
    }

    public static function decrementFavoriteNum($id, $num = 1)
    {
        return self::where('cid', $id)->decrement('favorite_num', $num);
    }

    public static function decrementLikeNum($id, $num = 1)
    {
        return self::where('cid', $id)->decrement('like_num', $num);
    }

    public function fields(): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(FieldsModel::class, 'cid', 'cid')
            ->whereNotIn('name', [
                'disableDarkMask',
                'enableFlowChat',
                'enableMathJax',
                'enableMermaid',
                'TOC',
            ]);
    }

    public function author(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this
            ->hasOne(UsersModel::class, 'uid', 'authorId')
            ->withDefault(function (){
                return UsersModel::makeOnce(['uid' => 0, 'screenName' => ""]);
            });
    }


    public function relationships(
    ): \Illuminate\Database\Eloquent\Relations\HasMany
    {
        return $this->hasMany(RelationshipsModel::class, 'cid', 'cid');
    }

    public function fieldValue($name, $default = null)
    {
        static $fields = null;
        if ($fields === null) {
            $fields = $this->fields->keyBy('name');
        }
        /** @var FieldsModel $field */
        $field = $fields[$name] ?? null;
        if (!isset($field)) {
            return $default;
        }
        if ($field->type == FieldsModel::TYPE_STR) {
            return $field->str_value;
        } elseif ($field->type == FieldsModel::TYPE_INT) {
            return $field->int_value;
        } elseif ($field->type == FieldsModel::TYPE_FLOAT) {
            return $field->float_value;
        } else {
            return $default;
        }
    }

    public function loadTagWithCategory(): ContentsModel
    {
        if (!$this->relationLoaded('relationships')){
            $this->load([ 'relationships' => function ($query) {
                    $query->with('meta');
                },
            ]);
        }
        $tags = collect([]);
        $category = collect([]);
        foreach ($this->relationships as $relationship) {
            $meta = $relationship->meta;
            if ($meta->type == MetasModel::TYPE_CATEGORY) {
                $category->push($meta);
            } elseif ($meta->type == MetasModel::TYPE_TAG) {
                $tags->push($meta);
            }
        }
        $this->setRelation('category', $category);
        $this->setRelation('tags', $tags);
        $this->unsetRelation('relationships');

        return $this;
    }

    public function loadMarkdown()
    {
        $html = \tools\LibMarkdown::loadMarkdown($this->text);
        $this->setAttribute('content', $html);
        $this->makeHidden('text');
    }

    public function getViewAttribute()
    {
        return $this->attributes['fake_view'] ?? ceil($this->attributes['view'] * self::FAKE_VIEW_MULTIPLE);
    }

    public static function incrByView($cid){
        $key = "contents:view:key:" . $cid;
        $val = redis()->incrBy($key, 1);
        $val = intval($val);
        if ($val >= rand(50, 60)){
            //浏览数
            $contents = self::find($cid);
            if (!empty($contents)){
                $fake_view = ceil($val * self::FAKE_VIEW_MULTIPLE);
                $contents->increment('view', $val, ['fake_view' => \DB::raw('fake_view + ' . $fake_view)]);
                if ($contents->status == self::STATUS_PUBLISH){
                    //加入排行榜
                    self::addCacheData($cid, $val, $contents->getRawOriginal('created'));
                }
            }
            //清除redis
            redis()->del($key);
        }
    }

    /**
     * @throws Exception
     */
    public static function addCacheData($cid, $increase, $created){
        $increase = floatval($increase);
        $day = date('Ymd');
        $week = date('W');
        $month = date('Ym');
        $key_day = sprintf(self::CONTENTS_RANK_VIEW, $day);
        $key_week = sprintf(self::CONTENTS_RANK_VIEW, $week);
        $key_month = sprintf(self::CONTENTS_RANK_VIEW, $month);
        //日榜
        if (date('Ymd', $created) == $day){
            redis()->zIncrBy($key_day, $increase, $cid);
            self::keyTtl('day', $key_day);
        }
        //周榜
        if (date('W', $created) == $week){
            redis()->zIncrBy($key_week, $increase, $cid);
            self::keyTtl('week', $key_week);
        }
        //月榜
        if (date('Ym', $created) == $month){
            redis()->zIncrBy($key_month, $increase, $cid);
            self::keyTtl('month', $key_month);
        }
    }

    /**
     * @throws RedisException
     */
    public static function keyTtl($type, $key){
        switch ($type){
            case 'day':
                if (redis()->ttl($key) == -1){
                    redis()->expire($key, 25 * 3600);
                }
                break;
            case 'week':
                if (redis()->ttl($key) == -1){
                    redis()->expire($key, 8 * 24 * 3600);
                }
                break;
            case 'month':
                if (redis()->ttl($key) == -1){
                    redis()->expire($key, 32 * 24 * 3600);
                }
                break;
            default:
                break;
        }
    }

    public static function hotTags(){
        $day = date('Ymd');
        return cached('hot:view:tags' . $day)
            ->fetchJson(function () use ($day){
                $key_day = sprintf(self::CONTENTS_RANK_VIEW, $day);
                $cids = redis()->zRevRange($key_day, 0, 9, ['withscores' => true]);
                $cids = array_keys($cids);
                $tags = [];
                if ($cids){
                    $list = ContentsModel::query()
                        ->with([
                            'relationships' => function ($query) {
                                $query->with('meta');
                            },
                        ])
                        ->selectRaw("cid")
                        ->whereIn('cid', $cids)
                        ->get()
                        ->each(function (ContentsModel $model) {
                            $model->loadTagWithCategory();
                        });
                    collect($list)->map(function ($item) use (&$tags){
                        collect($item->tags)->map(function ($val) use (&$tags){
                            $tags[] = [
                                'mid' => $val->mid,
                                'name' => $val->name,
                            ];
                        });
                    });
                    $tags = array_unique($tags, SORT_REGULAR);
                    if (count($tags) > 8){
                        $tags = collect($tags)->random(8)->toArray();
                    }
                }
                if (empty($tags)){
                    $tags = MetasModel::where("type",MetasModel::TYPE_TAG)
                        ->limit(8)
                        ->orderByDesc("count")->get(["mid","name"])->toArray();
                }
                return $tags;
            });
    }

    public static function hotSearch($limit = 4){
        $day = date('Ymd');
        return cached('hot:search:'.$limit)
            ->fetchJson(function () use ($day,$limit){
                $key_day = sprintf(self::CONTENTS_RANK_VIEW, $day);
                $cids = redis()->zRevRange($key_day, 0, 9, ['withscores' => true]);
                $cids = array_keys($cids);
                if (empty($cids)){
                    $times = [time()-86400*5,time()];
                    $cids = ContentsModel::query()
                            ->whereBetween('created',$times)
                            ->where('type', ContentsModel::TYPE_POST)
                            ->where('status', ContentsModel::STATUS_PUBLISH)
                            ->where('is_slice', 1)
                            ->where('app_hide', ContentsModel::APP_HIDE_NO)
                            //->orderByDesc('view_ct')
                            ->pluck("cid")->toArray();
                }
                if ($cids){
                    shuffle($cids);
                    $randids = array_slice($cids, 0, 8);
                    $list = ContentsModel::query()
                        ->selectRaw("cid,title,created,`order`,type,status,commentsNum,is_home,home_top,is_slice,authorId,view,fake_view")
                        ->whereIn('cid', $randids)
                        ->orderByDesc("fake_view")
                        ->limit($limit)
                        ->get()
                        ->each(function (\ContentsModel $item) {
                            $item->hotValue = $item->fake_view * 99 + 45621;
                        });
                }
                return $list;
            });
    }
}