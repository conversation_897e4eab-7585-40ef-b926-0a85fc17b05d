<?php

namespace database;

use Illuminate\Database\Connectors\MySqlConnector;
use PDO;

/**
 * 自定义MySQL连接器，修复 "Cannot execute queries while other unbuffered queries are active" 错误
 */
class CustomMySqlConnector extends MySqlConnector
{
    /**
     * 建立数据库连接
     *
     * @param  array  $config
     * @return \PDO
     */
    public function connect(array $config)
    {
        // 关键修复：在DSN中包含数据库名，这样就不需要执行 "use database" 语句
        $dsn = $this->getDsn($config);

        $options = $this->getOptions($config);

        // 强制设置缓冲查询选项
        $options[PDO::MYSQL_ATTR_USE_BUFFERED_QUERY] = true;
        $options[PDO::ATTR_EMULATE_PREPARES] = true;
        $options[PDO::MYSQL_ATTR_MULTI_STATEMENTS] = false;
        $options[PDO::ATTR_AUTOCOMMIT] = true;

        // 创建连接
        $connection = $this->createConnection($dsn, $config, $options);

        // 关键修复：不执行 "use database" 语句，因为数据库名已经在DSN中指定
        // 这避免了原始MySqlConnector中的 exec("use database") 调用

        // 直接配置其他选项
        $this->configureIsolationLevel($connection, $config);
        $this->configureEncoding($connection, $config);
        $this->configureTimezone($connection, $config);
        $this->setModes($connection, $config);

        return $connection;
    }

    /**
     * 创建PDO连接
     *
     * @param  string  $dsn
     * @param  array  $config
     * @param  array  $options
     * @return \PDO
     */
    protected function createConnection($dsn, array $config, array $options)
    {
        [$username, $password] = [
            $config['username'] ?? null, $config['password'] ?? null,
        ];

        try {
            $pdo = $this->createPdoConnection($dsn, $username, $password, $options);
            
            // 连接建立后立即设置缓冲查询属性
            $pdo->setAttribute(PDO::MYSQL_ATTR_USE_BUFFERED_QUERY, true);
            $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, true);
            
            return $pdo;
        } catch (\Exception $e) {
            return $this->tryAgainIfCausedByLostConnection(
                $e, $dsn, $username, $password, $options
            );
        }
    }

    /**
     * 获取DSN字符串，确保包含数据库名
     *
     * @param  array  $config
     * @return string
     */
    protected function getDsn(array $config)
    {
        // 确保DSN中包含数据库名，这样就不需要后续的 "use database" 语句
        $dsn = isset($config['port'])
                    ? "mysql:host={$config['host']};port={$config['port']};dbname={$config['database']}"
                    : "mysql:host={$config['host']};dbname={$config['database']}";

        // 添加字符集
        if (isset($config['charset'])) {
            $dsn .= ";charset={$config['charset']}";
        }

        // 添加unix socket支持
        if (isset($config['unix_socket']) && $config['unix_socket']) {
            $dsn = "mysql:unix_socket={$config['unix_socket']};dbname={$config['database']}";
            if (isset($config['charset'])) {
                $dsn .= ";charset={$config['charset']}";
            }
        }

        return $dsn;
    }
}
