<?php
namespace service;

use tools\HttpCurl;

class MelonService
{
    public static function replaceContentImgAndVideo($content, $cover)
    {
        $prefixConf  = url_prefix();
        $videoPrefix = $prefixConf['video_prefix'];
        $imagePrefix = $prefixConf['image_prefix'];

        // 替换img标签中的src值
        $content = preg_replace_callback('/<img src="([^"]+)"/', function($matches) use ($imagePrefix) {
            // 图片渲染区分 web 和 admin 后台
            return '<img src="' . "{$imagePrefix}/".  trim($matches[1], '/') . '"';
        }, $content);

        $cover   = url_image($cover);
        // 替换data-url属性中的值 url_pc_video
        $content = preg_replace_callback('/data-url="([^"]+)"/', function($matches) use($cover) {

            $url      = !empty($matches[1]) ? "/".trim($matches[1], '/') : '';
            $videoUrl = url_pc_video($url);

            return <<<HTML
data-url="{$videoUrl}"
HTML;

        }, $content);

        $content = str_replace("<p><br></p>", '', $content);
        $content = str_replace('<div class="ql-video bg-base1"><div class="ql-video-mse" data-url=', "<video pic='{$cover}' src=", $content);
        $content = str_replace('</div> </div>', '</video>', $content);


        return $content;
    }

    private static function repairVideoUrl($url, $videoPrefix): string
    {
        $url = "{$videoPrefix}/".trim($url, '/');
        $hash = bdHash($url,parse_url($url,PHP_URL_PATH));
        return "{$url}?{$hash}";
    }


}
